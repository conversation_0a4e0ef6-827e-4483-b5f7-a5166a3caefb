//
//  StudentLesson.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import Foundation

// MARK: - Student Lesson Models

struct StudentLesson: Codable, Identifiable, Equatable {
    let id: Int
    let name: String
    let className: String
    let subjectName: String
    let instructorName: String
    let startDatetime: String
    let endDatetime: String
    let durationHours: Double
    let room: String
    let location: String
    let attendanceStatus: StudentAttendanceStatus
    let lessonStatus: LessonStatus
    let canAccess: Bool
    let needsAttention: Bool
    let attentionReason: String?

    // API-provided computed values (more accurate than client-side computation)
    let isToday: Bool
    let isUpcoming: Bool
    let isLive: Bool

    // Optional fields for backward compatibility
    let lessonNumber: Int?
    let hasAssignments: Bool?
    let pendingAssignments: Int?
    let imageUrl: String?
    
    enum CodingKeys: String, CodingKey {
        case id, name
        case className = "class_name"
        case subjectName = "subject_name"
        case instructorName = "instructor_name"
        case startDatetime = "start_datetime"
        case endDatetime = "end_datetime"
        case durationHours = "duration_hours"
        case room, location
        case attendanceStatus = "attendance_status"
        case lessonStatus = "lesson_status"
        case canAccess = "can_access"
        case needsAttention = "needs_attention"
        case attentionReason = "attention_reason"
        case isToday = "is_today"
        case isUpcoming = "is_upcoming"
        case isLive = "is_live"

        // Optional fields
        case lessonNumber = "lesson_number"
        case hasAssignments = "has_assignments"
        case pendingAssignments = "pending_assignments"
        case imageUrl = "image_url"
    }
}

// MARK: - Supporting Enums

enum StudentAttendanceStatus: String, Codable, CaseIterable {
    case present = "present"
    case absent = "absent"
    case pending = "pending"

    var displayName: String {
        switch self {
        case .present: return "Có mặt"
        case .absent: return "Vắng mặt"
        case .pending: return "Chưa điểm danh"
        }
    }

    var color: String {
        switch self {
        case .present: return "success"
        case .absent: return "error"
        case .pending: return "warning"
        }
    }
}

enum LessonStatus: String, Codable, CaseIterable {
    case scheduled = "scheduled"
    case completed = "completed"
    case cancelled = "cancelled"
    case inProgress = "in_progress"
    
    var displayName: String {
        switch self {
        case .scheduled: return "Đã lên lịch"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        case .inProgress: return "Đang diễn ra"
        }
    }
}

enum LessonType: String, CaseIterable {
    case upcoming = "upcoming"
    case completed = "completed"
    case cancelled = "cancelled"
    case live = "live"
    case today = "today"
    case needsAttention = "needs_attention"
    
    var displayName: String {
        switch self {
        case .upcoming: return "Sắp tới"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        case .live: return "Đang diễn ra"
        case .today: return "Hôm nay"
        case .needsAttention: return "Cần chú ý"
        }
    }
    
    var priority: Int {
        switch self {
        case .live: return 100
        case .needsAttention: return 90
        case .today: return 80
        case .upcoming: return 70
        case .completed: return 60
        case .cancelled: return 50
        }
    }
}

// MARK: - Schedule Filter Types

enum ScheduleFilter: String, CaseIterable {
    case all = "all"
    case today = "today"
    case upcoming = "upcoming"
    case live = "live"
    
    var displayName: String {
        switch self {
        case .all: return "Tất cả"
        case .today: return "Hôm nay"
        case .upcoming: return "Sắp tới"
        case .live: return "Đang diễn ra"
        }
    }
}



// MARK: - API Response Models

struct StudentLessonsResponse: Codable {
    let success: Bool
    let message: String
    let data: [StudentLesson]
    let pagination: PaginationInfo?
}

// PaginationInfo is now defined in Core/Network/Models/PaginationInfo.swift

// MARK: - Check-in Models

struct CheckinRequest: Codable {
    let lessonId: Int
    let locationLat: Double
    let locationLng: Double
    let deviceInfo: String
    let notes: String?

    enum CodingKeys: String, CodingKey {
        case lessonId = "lesson_id"
        case locationLat = "location_lat"
        case locationLng = "location_lng"
        case deviceInfo = "device_info"
        case notes
    }
}

struct LocationData: Codable {
    let latitude: Double
    let longitude: Double
}

struct LessonDeviceInfo: Codable {
    let deviceType: String
    let deviceId: String
    let appVersion: String

    enum CodingKeys: String, CodingKey {
        case deviceType = "device_type"
        case deviceId = "device_id"
        case appVersion = "app_version"
    }
}

struct CheckinResponse: Codable {
    let success: Bool
    let message: String
    let data: CheckinData?
}

struct CheckinData: Codable {
    let attendanceId: Int
    let status: CheckinStatus
    let checkedInAt: String
    let locationVerified: Bool
    
    enum CodingKeys: String, CodingKey {
        case attendanceId = "attendance_id"
        case status
        case checkedInAt = "checked_in_at"
        case locationVerified = "location_verified"
    }
}

enum CheckinStatus: String, Codable {
    case success = "success"
    case failed = "failed"
    case alreadyCheckedIn = "already_checked_in"
    
    var displayMessage: String {
        switch self {
        case .success: return "Điểm danh thành công!"
        case .failed: return "Điểm danh thất bại. Vui lòng thử lại."
        case .alreadyCheckedIn: return "Bạn đã điểm danh cho buổi học này."
        }
    }
}



// MARK: - Date Helper

struct LessonDateHelper {
    private static let formatter: ISO8601DateFormatter = {
        let formatter = ISO8601DateFormatter()
        return formatter
    }()

    static func parseDateTime(_ dateTimeString: String) -> Date? {
        // Try ISO8601 with timezone first
        if let result = formatter.date(from: dateTimeString) {
            return result
        }

        // Try format without timezone (assume UTC): "2025-06-17T18:00:00"
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        dateFormatter.timeZone = TimeZone(identifier: "UTC")

        return dateFormatter.date(from: dateTimeString)
    }

    static func isToday(_ dateTimeString: String) -> Bool {
        guard let date = parseDateTime(dateTimeString) else { return false }
        return Calendar.current.isDateInToday(date)
    }

    static func isUpcoming(_ dateTimeString: String) -> Bool {
        guard let date = parseDateTime(dateTimeString) else { return false }
        let now = Date()
        return date > now && !Calendar.current.isDateInToday(date)
    }

    static func isLive(_ startDateTimeString: String, _ endDateTimeString: String) -> Bool {
        guard let startDate = parseDateTime(startDateTimeString) else { return false }

        let now = Date()

        // If we have end date, check if now is between start and end
        if let endDate = parseDateTime(endDateTimeString) {
            return now >= startDate && now <= endDate
        }

        // If no end date, assume lesson is live if it started today and within reasonable hours
        if Calendar.current.isDate(startDate, inSameDayAs: now) {
            let timeSinceStart = now.timeIntervalSince(startDate)
            // Consider live if started within last 4 hours
            return timeSinceStart >= 0 && timeSinceStart <= 4 * 3600
        }

        return false
    }
}
