//
//  Student.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> App on 23/7/25.
//

import Foundation

// MARK: - Student Model
struct Student: Codable, Identifiable {
    let id: String
    let studentId: String // Student ID number
    let email: String
    let firstName: String
    let lastName: String
    let phone: String?
    let avatar: String?
    let dateOfBirth: Date?
    let gender: Gender?
    let address: String?
    let emergencyContact: EmergencyContact?
    let enrollmentDate: Date
    let status: StudentStatus
    let gpa: Double?
    let totalCredits: Int
    let completedCredits: Int
    let major: String?
    let year: AcademicYear?
    let courses: [StudentCourse]?
    let metadata: [String: AnyCodable]?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var displayName: String {
        return "\(firstName) \(lastName)"
    }
    
    var isActive: Bool {
        return status == .active
    }
    
    var progressPercentage: Double {
        guard totalCredits > 0 else { return 0 }
        return Double(completedCredits) / Double(totalCredits) * 100
    }
    
    var academicStanding: AcademicStanding {
        guard let gpa = gpa else { return .unknown }
        
        switch gpa {
        case 3.5...4.0: return .excellent
        case 3.0..<3.5: return .good
        case 2.5..<3.0: return .satisfactory
        case 2.0..<2.5: return .probation
        default: return .failing
        }
    }
}

// MARK: - Gender
enum Gender: String, Codable, CaseIterable {
    case male = "MALE"
    case female = "FEMALE"
    case other = "OTHER"
    case preferNotToSay = "PREFER_NOT_TO_SAY"
    
    var displayName: String {
        switch self {
        case .male: return "Nam"
        case .female: return "Nữ"
        case .other: return "Khác"
        case .preferNotToSay: return "Không muốn tiết lộ"
        }
    }
}

// MARK: - Student Status
enum StudentStatus: String, Codable, CaseIterable {
    case active = "ACTIVE"
    case inactive = "INACTIVE"
    case graduated = "GRADUATED"
    case suspended = "SUSPENDED"
    case transferred = "TRANSFERRED"
    case dropped = "DROPPED"
    
    var displayName: String {
        switch self {
        case .active: return "Đang học"
        case .inactive: return "Tạm nghỉ"
        case .graduated: return "Đã tốt nghiệp"
        case .suspended: return "Bị đình chỉ"
        case .transferred: return "Chuyển trường"
        case .dropped: return "Bỏ học"
        }
    }
    
    var color: String {
        switch self {
        case .active: return "green"
        case .inactive: return "orange"
        case .graduated: return "blue"
        case .suspended: return "red"
        case .transferred: return "purple"
        case .dropped: return "gray"
        }
    }
}

// MARK: - Academic Year
enum AcademicYear: String, Codable, CaseIterable {
    case freshman = "FRESHMAN"
    case sophomore = "SOPHOMORE"
    case junior = "JUNIOR"
    case senior = "SENIOR"
    case graduate = "GRADUATE"
    
    var displayName: String {
        switch self {
        case .freshman: return "Năm 1"
        case .sophomore: return "Năm 2"
        case .junior: return "Năm 3"
        case .senior: return "Năm 4"
        case .graduate: return "Sau đại học"
        }
    }
    
    var order: Int {
        switch self {
        case .freshman: return 1
        case .sophomore: return 2
        case .junior: return 3
        case .senior: return 4
        case .graduate: return 5
        }
    }
}

// MARK: - Academic Standing
enum AcademicStanding: String, Codable, CaseIterable {
    case excellent = "EXCELLENT"
    case good = "GOOD"
    case satisfactory = "SATISFACTORY"
    case probation = "PROBATION"
    case failing = "FAILING"
    case unknown = "UNKNOWN"
    
    var displayName: String {
        switch self {
        case .excellent: return "Xuất sắc"
        case .good: return "Giỏi"
        case .satisfactory: return "Khá"
        case .probation: return "Cảnh báo"
        case .failing: return "Yếu"
        case .unknown: return "Chưa xác định"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .satisfactory: return "orange"
        case .probation: return "yellow"
        case .failing: return "red"
        case .unknown: return "gray"
        }
    }
}

// MARK: - Emergency Contact
struct EmergencyContact: Codable {
    let name: String
    let relationship: String
    let phone: String
    let email: String?
    let address: String?
}

// MARK: - Student Course (API Response Model)
struct StudentCourse: Codable, Identifiable {
    let id: Int
    let name: String
    let code: String
    let description: String?
    let shortDescription: String?
    let level: String?
    let durationHours: Double?
    let subjectName: String?
    let category: String?
    let originalPrice: Double?
    let discountedPrice: Double?
    let hasDiscount: Bool
    let currencyCode: String

    // Enrollment Information
    let enrollmentId: Int
    let enrollmentDate: String
    let enrollmentStatus: EnrollmentStatus
    let paymentStatus: PaymentStatus

    // Class Information
    let hasClass: Bool
    let classId: Int?
    let className: String?
    let classStatus: String? // Use String to avoid conflict with existing ClassStatus

    // Progress Information
    let progressPercentage: Double?
    let attendanceRate: Double?
    let currentGrade: String?
    let isCurrent: Bool
    let isCompleted: Bool
    let canAccess: Bool
    let needsAttention: Bool
    let attentionReason: String?

    // Timestamps
    let createdAt: String?
    let updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id, name, code, description, level, category
        case shortDescription = "short_description"
        case durationHours = "duration_hours"
        case subjectName = "subject_name"
        case originalPrice = "original_price"
        case discountedPrice = "discounted_price"
        case hasDiscount = "has_discount"
        case currencyCode = "currency_code"
        case enrollmentId = "enrollment_id"
        case enrollmentDate = "enrollment_date"
        case enrollmentStatus = "enrollment_status"
        case paymentStatus = "payment_status"
        case hasClass = "has_class"
        case classId = "class_id"
        case className = "class_name"
        case classStatus = "class_status"
        case progressPercentage = "progress_percentage"
        case attendanceRate = "attendance_rate"
        case currentGrade = "current_grade"
        case isCurrent = "is_current"
        case isCompleted = "is_completed"
        case canAccess = "can_access"
        case needsAttention = "needs_attention"
        case attentionReason = "attention_reason"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Enrollment Status
enum EnrollmentStatus: String, Codable, CaseIterable {
    case enrolled = "enrolled"
    case pending = "pending"
    case cancelled = "cancelled"
    case completed = "completed"
    case unknown = "unknown"

    // Custom initializer to handle unknown values
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let rawValue = try container.decode(String.self)
        self = EnrollmentStatus(rawValue: rawValue) ?? .unknown
    }

    var displayName: String {
        switch self {
        case .enrolled: return "Đã đăng ký"
        case .pending: return "Đang chờ"
        case .cancelled: return "Đã hủy"
        case .completed: return "Hoàn thành"
        case .unknown: return "Không xác định"
        }
    }

    var color: String {
        switch self {
        case .enrolled: return "green"
        case .pending: return "orange"
        case .cancelled: return "red"
        case .completed: return "blue"
        case .unknown: return "gray"
        }
    }
}

// MARK: - Payment Status
enum PaymentStatus: String, Codable, CaseIterable {
    case paid = "paid"
    case pending = "pending"
    case overdue = "overdue"
    case cancelled = "cancelled"
    case unknown = "unknown"

    // Custom initializer to handle unknown values
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let rawValue = try container.decode(String.self)
        self = PaymentStatus(rawValue: rawValue) ?? .unknown
    }

    var displayName: String {
        switch self {
        case .paid: return "Đã thanh toán"
        case .pending: return "Chờ thanh toán"
        case .overdue: return "Quá hạn"
        case .cancelled: return "Đã hủy"
        case .unknown: return "Không xác định"
        }
    }

    var color: String {
        switch self {
        case .paid: return "green"
        case .pending: return "orange"
        case .overdue: return "red"
        case .cancelled: return "gray"
        case .unknown: return "gray"
        }
    }
}

// MARK: - Student Course List Response
struct StudentCourseListResponse: Codable {
    let success: Bool
    let message: String
    let data: [StudentCourse]
    let pagination: PaginationMetadata
}

// MARK: - Student Course Detail Response
struct StudentCourseDetailResponse: Codable {
    let success: Bool
    let message: String
    let data: StudentCourse?
    let meta: StudentResponseMeta?
}

// MARK: - Student Response Meta
struct StudentResponseMeta: Codable {
    let timestamp: String?
    let apiVersion: String?
    let service: String?
    let errorCode: String?

    enum CodingKeys: String, CodingKey {
        case timestamp
        case apiVersion = "api_version"
        case service
        case errorCode = "error_code"
    }
}

// MARK: - Pagination Metadata
struct PaginationMetadata: Codable {
    let page: Int
    let pageSize: Int
    let totalCount: Int
    let totalPages: Int
    let hasNext: Bool
    let hasPrevious: Bool

    enum CodingKeys: String, CodingKey {
        case page
        case pageSize = "page_size"
        case totalCount = "total_count"
        case totalPages = "total_pages"
        case hasNext = "has_next"
        case hasPrevious = "has_previous"
        // Alternative keys
        case totalItems = "total_items"
        case totalRecords = "total_records"
        case count = "count"
    }

    // Custom initializer to handle different key variations
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        page = try container.decode(Int.self, forKey: .page)
        pageSize = try container.decode(Int.self, forKey: .pageSize)
        totalPages = try container.decode(Int.self, forKey: .totalPages)
        hasNext = try container.decode(Bool.self, forKey: .hasNext)
        hasPrevious = try container.decode(Bool.self, forKey: .hasPrevious)

        // Try different keys for totalCount
        if let count = try? container.decode(Int.self, forKey: .totalCount) {
            totalCount = count
        } else if let count = try? container.decode(Int.self, forKey: .totalItems) {
            totalCount = count
        } else if let count = try? container.decode(Int.self, forKey: .totalRecords) {
            totalCount = count
        } else if let count = try? container.decode(Int.self, forKey: .count) {
            totalCount = count
        } else {
            totalCount = 0 // Default fallback
        }
    }

    // Custom encoder to maintain compatibility
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(page, forKey: .page)
        try container.encode(pageSize, forKey: .pageSize)
        try container.encode(totalCount, forKey: .totalCount)
        try container.encode(totalPages, forKey: .totalPages)
        try container.encode(hasNext, forKey: .hasNext)
        try container.encode(hasPrevious, forKey: .hasPrevious)
    }
}

// MARK: - StudentCourse Extensions
extension StudentCourse {
    var formattedPrice: String {
        let price = hasDiscount ? (discountedPrice ?? originalPrice ?? 0) : (originalPrice ?? 0)
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currencyCode
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter.string(from: NSNumber(value: price)) ?? "\(price) \(currencyCode)"
    }

    var formattedOriginalPrice: String? {
        guard let originalPrice = originalPrice, hasDiscount else { return nil }
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currencyCode
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter.string(from: NSNumber(value: originalPrice))
    }

    var statusColor: String {
        if needsAttention {
            return "red"
        } else if isCurrent {
            return "green"
        } else if isCompleted {
            return "blue"
        } else {
            return "gray"
        }
    }

    var statusText: String {
        if needsAttention {
            return attentionReason ?? "Cần chú ý"
        } else if isCurrent {
            return "Đang học"
        } else if isCompleted {
            return "Hoàn thành"
        } else {
            return enrollmentStatus.displayName
        }
    }

    var progressText: String {
        if let progress = progressPercentage {
            return String(format: "%.1f%%", progress)
        }
        return "Chưa có dữ liệu"
    }

    var attendanceText: String {
        if let attendance = attendanceRate {
            return String(format: "%.1f%%", attendance)
        }
        return "Chưa có dữ liệu"
    }


}

// MARK: - Assignment Score
struct AssignmentScore: Codable, Identifiable {
    let id: String
    let assignmentId: String
    let assignmentName: String
    let score: Double
    let maxScore: Double
    let submittedAt: Date?
    let gradedAt: Date?
    let feedback: String?
    
    var percentage: Double {
        guard maxScore > 0 else { return 0 }
        return (score / maxScore) * 100
    }
}

// MARK: - Exam Score
struct ExamScore: Codable, Identifiable {
    let id: String
    let examId: String
    let examName: String
    let examType: ExamType
    let score: Double
    let maxScore: Double
    let examDate: Date
    let gradedAt: Date?
    
    var percentage: Double {
        guard maxScore > 0 else { return 0 }
        return (score / maxScore) * 100
    }
}

// MARK: - Exam Type
enum ExamType: String, Codable, CaseIterable {
    case quiz = "QUIZ"
    case midterm = "MIDTERM"
    case final = "FINAL"
    case practical = "PRACTICAL"
    
    var displayName: String {
        switch self {
        case .quiz: return "Kiểm tra"
        case .midterm: return "Giữa kỳ"
        case .final: return "Cuối kỳ"
        case .practical: return "Thực hành"
        }
    }
}

// MARK: - Student Extensions
extension Student {
    static let mockStudents: [Student] = [
        Student(
            id: "student_1",
            studentId: "2024001",
            email: "<EMAIL>",
            firstName: "Nguyễn",
            lastName: "Văn B",
            phone: "+84901234567",
            avatar: nil,
            dateOfBirth: Date().addingTimeInterval(-20 * 365 * 24 * 3600), // 20 years ago
            gender: .male,
            address: "123 Đường XYZ, Quận 1, TP.HCM",
            emergencyContact: EmergencyContact(
                name: "Nguyễn Thị C",
                relationship: "Mẹ",
                phone: "+84907654321",
                email: "<EMAIL>",
                address: "123 Đường XYZ, Quận 1, TP.HCM"
            ),
            enrollmentDate: Date().addingTimeInterval(-2 * 365 * 24 * 3600), // 2 years ago
            status: .active,
            gpa: 3.75,
            totalCredits: 120,
            completedCredits: 80,
            major: "Computer Science",
            year: .junior,
            courses: nil,
            metadata: nil,
            createdAt: Date().addingTimeInterval(-2 * 365 * 24 * 3600),
            updatedAt: Date()
        ),
        Student(
            id: "student_2",
            studentId: "2024002",
            email: "<EMAIL>",
            firstName: "Trần",
            lastName: "Thị D",
            phone: "+84902345678",
            avatar: nil,
            dateOfBirth: Date().addingTimeInterval(-19 * 365 * 24 * 3600), // 19 years ago
            gender: .female,
            address: "456 Đường ABC, Quận 2, TP.HCM",
            emergencyContact: EmergencyContact(
                name: "Trần Văn E",
                relationship: "Bố",
                phone: "+84908765432",
                email: "<EMAIL>",
                address: "456 Đường ABC, Quận 2, TP.HCM"
            ),
            enrollmentDate: Date().addingTimeInterval(-2 * 365 * 24 * 3600), // 2 years ago
            status: .active,
            gpa: 3.85,
            totalCredits: 120,
            completedCredits: 85,
            major: "Computer Science",
            year: .junior,
            courses: nil,
            metadata: nil,
            createdAt: Date().addingTimeInterval(-2 * 365 * 24 * 3600),
            updatedAt: Date()
        )
    ]
}
