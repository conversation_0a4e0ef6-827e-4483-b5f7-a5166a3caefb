//
//  StudentScheduleService.swift
//  VantisEducation
//
//  Created by Mobile App Template on 31/7/25.
//

import Foundation

// MARK: - Schedule View Types

enum ScheduleViewType: String, CaseIterable {
    case day = "day"
    case week = "week"
    case month = "month"
    
    var displayName: String {
        switch self {
        case .day: return "Ngày"
        case .week: return "Tuần"
        case .month: return "Tháng"
        }
    }
}

// MARK: - Schedule Request Parameters

struct ScheduleRequest {
    let view: ScheduleViewType
    let dateFrom: Date?
    let dateTo: Date?
    let courseId: Int?
    let classId: Int?
    let page: Int
    let pageSize: Int
    
    init(
        view: ScheduleViewType = .week,
        dateFrom: Date? = nil,
        dateTo: Date? = nil,
        courseId: Int? = nil,
        classId: Int? = nil,
        page: Int = 1,
        pageSize: Int = 50
    ) {
        self.view = view
        self.dateFrom = dateFrom
        self.dateTo = dateTo
        self.courseId = courseId
        self.classId = classId
        self.page = page
        self.pageSize = pageSize
    }
}

// MARK: - Calendar Event Model

struct CalendarEvent: Identifiable, Equatable {
    let id: String
    let lessonId: Int
    let title: String
    let subtitle: String
    let startDate: Date
    let endDate: Date
    let location: String?
    let instructor: String
    let courseColor: String
    let lessonType: LessonType
    let attendanceStatus: StudentAttendanceStatus
    let canCheckin: Bool
    let originalLesson: StudentLesson
    
    // Calendar specific properties
    var duration: TimeInterval {
        endDate.timeIntervalSince(startDate)
    }
    
    var isToday: Bool {
        Calendar.current.isDateInToday(startDate)
    }
    
    var isUpcoming: Bool {
        startDate > Date()
    }
    
    var isLive: Bool {
        let now = Date()
        return startDate <= now && endDate >= now
    }
}

// MARK: - Schedule Service Protocol

protocol StudentScheduleServiceProtocol {
    func getSchedule(request: ScheduleRequest) async throws -> StudentLessonsResponse
    func getCalendarEvents(request: ScheduleRequest) async throws -> [CalendarEvent]
    func refreshSchedule(request: ScheduleRequest) async throws -> StudentLessonsResponse
}

// MARK: - Student Schedule Service

class StudentScheduleService: StudentScheduleServiceProtocol {
    private let apiClient: APIClient
    private let tokenManager: SecureTokenManager
    
    init(apiClient: APIClient = APIClient.shared, tokenManager: SecureTokenManager = SecureTokenManager.shared) {
        self.apiClient = apiClient
        self.tokenManager = tokenManager
    }
    
    // MARK: - Get Schedule
    
    func getSchedule(request: ScheduleRequest) async throws -> StudentLessonsResponse {
        var parameters: [String: Any] = [
            "view": request.view.rawValue,
            "page": request.page,
            "page_size": request.pageSize
        ]
        
        // Add optional parameters
        if let dateFrom = request.dateFrom {
            parameters["date_from"] = formatDate(dateFrom)
        }
        
        if let dateTo = request.dateTo {
            parameters["date_to"] = formatDate(dateTo)
        }
        
        if let courseId = request.courseId {
            parameters["course_id"] = courseId
        }
        
        if let classId = request.classId {
            parameters["class_id"] = classId
        }
        
        do {
            print("🔥 Schedule API Call - Endpoint: /students/my-schedule")
            print("🔥 Schedule API Call - Parameters: \(parameters)")

            let response: StudentLessonsResponse = try await apiClient.request(
                endpoint: "/students/my-schedule",
                method: .GET,
                parameters: parameters,
                responseType: StudentLessonsResponse.self,
                requiresAuth: true
            )

            print("🔥 Schedule API Response - Success: \(response.success)")
            print("🔥 Schedule API Response - Message: \(response.message)")
            print("🔥 Schedule API Response - Data count: \(response.data.count)")
            
            return response
        } catch {
            print("❌ StudentScheduleService: Failed to fetch schedule - \(error)")
            throw StudentScheduleError.fetchFailed(error.localizedDescription)
        }
    }
    
    // MARK: - Get Calendar Events
    
    func getCalendarEvents(request: ScheduleRequest) async throws -> [CalendarEvent] {
        let response = try await getSchedule(request: request)
        
        guard response.success else {
            throw StudentScheduleError.fetchFailed(response.message)
        }
        
        return response.data.compactMap { lesson in
            convertLessonToCalendarEvent(lesson)
        }
    }
    
    // MARK: - Refresh Schedule
    
    func refreshSchedule(request: ScheduleRequest) async throws -> StudentLessonsResponse {
        return try await getSchedule(request: request)
    }
    
    // MARK: - Helper Methods
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    private func convertLessonToCalendarEvent(_ lesson: StudentLesson) -> CalendarEvent? {
        guard let startDate = parseDateTime(lesson.startDatetime),
              let endDate = parseDateTime(lesson.endDatetime) else {
            print("❌ Failed to parse dates for lesson: \(lesson.name)")
            return nil
        }
        
        return CalendarEvent(
            id: "lesson_\(lesson.id)",
            lessonId: lesson.id,
            title: lesson.name,
            subtitle: "\(lesson.className) • \(lesson.instructorName)",
            startDate: startDate,
            endDate: endDate,
            location: lesson.location,
            instructor: lesson.instructorName,
            courseColor: getCourseColor(for: lesson.className),
            lessonType: lesson.lessonType,
            attendanceStatus: lesson.attendanceStatus,
            canCheckin: lesson.canCheckin,
            originalLesson: lesson
        )
    }
    
    private func parseDateTime(_ dateTimeString: String) -> Date? {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter.date(from: dateTimeString)
    }
    
    private func getCourseColor(for className: String) -> String {
        // Generate consistent color based on class name hash
        let hash = className.hashValue
        let colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#98D8C8", "#F7DC6F"]
        return colors[abs(hash) % colors.count]
    }
}

// MARK: - Schedule Service Errors

enum StudentScheduleError: LocalizedError {
    case fetchFailed(String)
    case invalidDateRange
    case networkError
    case unauthorized
    
    var errorDescription: String? {
        switch self {
        case .fetchFailed(let message):
            return "Không thể tải lịch học: \(message)"
        case .invalidDateRange:
            return "Khoảng thời gian không hợp lệ"
        case .networkError:
            return "Lỗi kết nối mạng. Vui lòng kiểm tra internet."
        case .unauthorized:
            return "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."
        }
    }
}
