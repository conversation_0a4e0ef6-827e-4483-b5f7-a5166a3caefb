//
//  LessonAdapter.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import Foundation

// MARK: - Lesson Data Adapter

struct LessonAdapter {
    
    // MARK: - Convert API Data to UI Models
    

    
    // MARK: - Sorting and Filtering
    
    static func sortLessonsByPriority(_ lessons: [StudentLesson]) -> [StudentLesson] {
        return lessons.sorted { (a, b) in
            let priorityA = getLessonPriority(a)
            let priorityB = getLessonPriority(b)
            
            // Primary sort by priority
            if priorityA != priorityB {
                return priorityA > priorityB
            }
            
            // Secondary sort by date (earliest first)
            let dateA = parseDateTime(a.startDatetime) ?? Date.distantFuture
            let dateB = parseDateTime(b.startDatetime) ?? Date.distantFuture
            return dateA < dateB
        }
    }
    
    static func filterLessons(_ lessons: [StudentLesson], by filter: ScheduleFilter) -> [StudentLesson] {
        switch filter {
        case .all:
            return lessons
        case .today:
            return lessons.filter { $0.isToday }
        case .upcoming:
            return lessons.filter { $0.isUpcoming }
        case .live:
            return lessons.filter { $0.isLive }
        }
    }
    
    // MARK: - Helper Functions
    
    private static func getLessonType(_ lesson: StudentLesson) -> LessonType {
        if lesson.isLive {
            return .live
        } else if lesson.needsAttention {
            return .needsAttention
        } else if lesson.isToday {
            return .today
        } else if lesson.isUpcoming {
            return .upcoming
        } else if lesson.lessonStatus == .completed {
            return .completed
        } else if lesson.lessonStatus == .cancelled {
            return .cancelled
        } else {
            return .upcoming
        }
    }
    
    private static func getLessonPriority(_ lesson: StudentLesson) -> Int {
        return getLessonType(lesson).priority
    }
    
    private static func formatLessonDateTime(_ dateTimeString: String, _ durationHours: Double) -> String {
        guard let date = parseDateTime(dateTimeString) else {
            return "Thời gian không xác định"
        }
        
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "vi_VN")
        
        let calendar = Calendar.current
        let now = Date()
        
        if calendar.isDateInToday(date) {
            formatter.dateFormat = "HH:mm"
            let timeString = formatter.string(from: date)
            let endTime = calendar.date(byAdding: .minute, value: Int(durationHours * 60), to: date)!
            let endTimeString = formatter.string(from: endTime)
            return "Hôm nay, \(timeString) - \(endTimeString)"
        } else if calendar.isDateInTomorrow(date) {
            formatter.dateFormat = "HH:mm"
            let timeString = formatter.string(from: date)
            return "Ngày mai, \(timeString)"
        } else if calendar.isDateInYesterday(date) {
            formatter.dateFormat = "HH:mm"
            let timeString = formatter.string(from: date)
            return "Hôm qua, \(timeString)"
        } else {
            formatter.dateFormat = "dd/MM/yyyy, HH:mm"
            return formatter.string(from: date)
        }
    }
    
    private static func formatLocation(_ room: String, _ location: String?) -> String {
        if let location = location, !location.isEmpty {
            return "📍 \(room), \(location)"
        } else {
            return "📍 \(room)"
        }
    }
    
    private static func formatDuration(_ hours: Double) -> String {
        if hours == 1.0 {
            return "1 giờ"
        } else if hours < 1.0 {
            let minutes = Int(hours * 60)
            return "\(minutes) phút"
        } else {
            let wholeHours = Int(hours)
            let remainingMinutes = Int((hours - Double(wholeHours)) * 60)
            
            if remainingMinutes == 0 {
                return "\(wholeHours) giờ"
            } else {
                return "\(wholeHours) giờ \(remainingMinutes) phút"
            }
        }
    }
    
    private static func formatNotes(_ lesson: StudentLesson) -> String? {
        var notes: [String] = []
        
        if (lesson.hasAssignments ?? false) && (lesson.pendingAssignments ?? 0) > 0 {
            notes.append("\(lesson.pendingAssignments ?? 0) bài tập chưa hoàn thành")
        }
        
        if lesson.attendanceStatus == .absent {
            notes.append("Vắng mặt")
        } else if lesson.attendanceStatus == .present {
            notes.append("Đã điểm danh")
        }
        
        if lesson.needsAttention, let reason = lesson.attentionReason, !reason.isEmpty {
            notes.append(reason)
        }
        
        return notes.isEmpty ? nil : notes.joined(separator: " • ")
    }
    
    private static func canCheckinLesson(_ lesson: StudentLesson) -> Bool {
        // Can checkin if:
        // 1. Lesson is live OR today
        // 2. Attendance status is pending
        // 3. Lesson is not cancelled

        let canCheckin = (lesson.isLive || lesson.isToday) &&
                        (lesson.attendanceStatus == .pending || lesson.attendanceStatus == .absent) &&
                        lesson.lessonStatus != .cancelled

        print("🔍 LessonAdapter: Checkin eligibility for lesson \(lesson.id):")
        print("   - Name: \(lesson.name)")
        print("   - IsLive: \(lesson.isLive)")
        print("   - IsToday: \(lesson.isToday)")
        print("   - AttendanceStatus: \(lesson.attendanceStatus)")
        print("   - LessonStatus: \(lesson.lessonStatus)")
        print("   - CanCheckin: \(canCheckin)")

        return canCheckin
    }
    
    private static func parseDateTime(_ dateTimeString: String) -> Date? {
        print("🔍 LessonAdapter: Parsing datetime: '\(dateTimeString)'")

        // Try ISO8601 with timezone first
        let iso8601Formatter = ISO8601DateFormatter()
        if let result = iso8601Formatter.date(from: dateTimeString) {
            print("🔍 LessonAdapter: Parsed with ISO8601: \(result)")
            return result
        }

        // Try format without timezone (assume UTC): "2025-06-17T18:00:00"
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        dateFormatter.timeZone = TimeZone(identifier: "UTC")

        if let result = dateFormatter.date(from: dateTimeString) {
            print("🔍 LessonAdapter: Parsed with custom formatter (UTC): \(result)")
            return result
        }

        print("🔍 LessonAdapter: Failed to parse datetime: '\(dateTimeString)'")
        return nil
    }
}





// MARK: - Device Info Helper

extension LessonAdapter {
    
    static func getCurrentDeviceInfo() -> LessonDeviceInfo {
        return LessonDeviceInfo(
            deviceType: "mobile",
            deviceId: getDeviceId(),
            appVersion: getAppVersion()
        )
    }
    
    private static func getDeviceId() -> String {
        // In a real app, you might use:
        // - UIDevice.current.identifierForVendor?.uuidString
        // - A stored UUID in Keychain
        // For now, return a mock ID
        return "iOS-Device-\(UUID().uuidString.prefix(8))"
    }
    
    private static func getAppVersion() -> String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
}

// MARK: - Date Utilities

extension LessonAdapter {
    
    static func isToday(_ dateString: String) -> Bool {
        guard let date = parseDateTime(dateString) else { return false }
        return Calendar.current.isDateInToday(date)
    }
    
    static func isUpcoming(_ dateString: String) -> Bool {
        guard let date = parseDateTime(dateString) else { return false }
        return date > Date()
    }
    
    static func isLive(_ lesson: StudentLesson) -> Bool {
        guard let startDate = parseDateTime(lesson.startDatetime) else { return false }
        let endDate = Calendar.current.date(byAdding: .minute, value: Int(lesson.durationHours * 60), to: startDate)!
        let now = Date()
        return startDate <= now && now <= endDate
    }
    
    static func formatRelativeDate(_ dateString: String) -> String {
        guard let date = parseDateTime(dateString) else { return "Không xác định" }
        
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "vi_VN")
        formatter.unitsStyle = .full
        
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}
