//
//  AboutView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showTermsOfService = false
    @State private var showPrivacyPolicy = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Custom Header
                headerSection
                
                // About Content
                aboutContent
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 20)
        }
        .navigationBarHidden(true)
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .sheet(isPresented: $showTermsOfService) {
            InAppBrowserView(
                url: URL(string: "https://vantis.edu.vn/terms")!,
                title: "Điều khoản sử dụng"
            )
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            InAppBrowserView(
                url: URL(string: "https://vantis.edu.vn/privacy")!,
                title: "<PERSON><PERSON>h sách bảo mật"
            )
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("About LinkX")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Learn more about us")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Empty space for balance
            Color.clear
                .frame(width: 36, height: 36)
        }
        .padding(.bottom, 8)
    }
    
    // MARK: - About Content
    private var aboutContent: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // App Info Section
            appInfoSection
            
            // Company Section
            companySection
            
            // Legal Section
            legalSection
        }
    }
    
    // MARK: - App Info Section
    private var appInfoSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("APP INFORMATION")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)
            
            // Section Content
            VStack(spacing: 0) {
                // App Version
                HStack(spacing: 16) {
                    // Icon
                    Image(systemName: "app.badge")
                        .font(.system(size: 20))
                        .foregroundColor(AppConstants.Colors.primary)
                        .frame(width: 24, height: 24)
                    
                    // Content
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Version")
                            .font(.beVietnamPro(.semiBold, size: 16))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("1.0.0 (Build 1)")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                
                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)
                
                // Release Date
                HStack(spacing: 16) {
                    // Icon
                    Image(systemName: "calendar")
                        .font(.system(size: 20))
                        .foregroundColor(AppConstants.Colors.primary)
                        .frame(width: 24, height: 24)
                    
                    // Content
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Release Date")
                            .font(.beVietnamPro(.semiBold, size: 16))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("July 2025")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Company Section Extension
extension AboutView {
    private var companySection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("COMPANY")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // About Us
                Button(action: {
                    // TODO: Navigate to about us
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "building.2")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("About Us")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Learn about our mission and values")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Team
                Button(action: {
                    // TODO: Navigate to team
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "person.3")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Our Team")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Meet the people behind LinkX")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Contact
                Button(action: {
                    if let url = URL(string: "mailto:<EMAIL>") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "envelope")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Contact Us")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("<EMAIL>")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // External Link Icon
                        Image(systemName: "arrow.up.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Legal Section Extension
extension AboutView {
    private var legalSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("LEGAL")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Terms of Service
                Button(action: {
                    showTermsOfService = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "doc.text")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Điều khoản sử dụng")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Đọc điều khoản và điều kiện sử dụng")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Privacy Policy
                Button(action: {
                    showPrivacyPolicy = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "shield")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Chính sách bảo mật")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Tìm hiểu cách chúng tôi bảo vệ dữ liệu của bạn")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Preview
#Preview {
    AboutView()
}
