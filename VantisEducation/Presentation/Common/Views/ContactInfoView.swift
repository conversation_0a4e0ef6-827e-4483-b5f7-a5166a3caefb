//
//  ContactInfoView.swift
//  VantisEducation
//
//  Created by Mobile App Template on 31/7/25.
//

import SwiftUI

struct ContactInfoView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "building.2.fill")
                            .font(.system(size: 60))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text(AppConstants.organizationName)
                            .font(AppConstants.Typography.title2)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .multilineTextAlignment(.center)
                        
                        Text(AppConstants.appTagline)
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    // Contact Information Cards
                    VStack(spacing: 16) {
                        // Phone Contact
                        ContactCard(
                            icon: "phone.fill",
                            title: "Hotline",
                            subtitle: AppConstants.Contact.supportPhone,
                            color: AppConstants.Colors.success
                        ) {
                            if let phoneURL = AppConstants.Contact.phoneURL {
                                UIApplication.shared.open(phoneURL)
                            }
                        }
                        
                        // Email Contact
                        ContactCard(
                            icon: "envelope.fill",
                            title: "Email Hỗ trợ",
                            subtitle: AppConstants.Contact.supportEmail,
                            color: AppConstants.Colors.primary
                        ) {
                            if let emailURL = AppConstants.Contact.emailURL {
                                UIApplication.shared.open(emailURL)
                            }
                        }
                        
                        // Training Email
                        ContactCard(
                            icon: "graduationcap.fill",
                            title: "Email Đào tạo",
                            subtitle: AppConstants.Contact.trainingEmail,
                            color: AppConstants.Colors.secondary
                        ) {
                            if let trainingEmailURL = AppConstants.Contact.trainingEmailURL {
                                UIApplication.shared.open(trainingEmailURL)
                            }
                        }
                        
                        // Address
                        ContactCard(
                            icon: "location.fill",
                            title: "Địa chỉ",
                            subtitle: AppConstants.Contact.fullAddress,
                            color: AppConstants.Colors.info
                        ) {
                            if let addressURL = AppConstants.Contact.addressURL {
                                UIApplication.shared.open(addressURL)
                            }
                        }
                        
                        // Website
                        ContactCard(
                            icon: "globe",
                            title: "Website",
                            subtitle: AppConstants.URLs.website,
                            color: AppConstants.Colors.accent
                        ) {
                            if let websiteURL = URL(string: AppConstants.URLs.website) {
                                UIApplication.shared.open(websiteURL)
                            }
                        }
                        
                        // Facebook
                        ContactCard(
                            icon: "person.2.fill",
                            title: "Facebook",
                            subtitle: "facebook.com/vantis.edu.vn",
                            color: Color.blue
                        ) {
                            if let facebookURL = URL(string: AppConstants.URLs.facebook) {
                                UIApplication.shared.open(facebookURL)
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    
                    Spacer(minLength: 20)
                }
            }
            .background(AppConstants.Colors.background)
            .navigationTitle("Liên hệ")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarItems(
                trailing: Button("Đóng") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(AppConstants.Colors.primary)
            )
        }
    }
}

struct ContactCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 40, height: 40)
                    .background(color.opacity(0.1))
                    .cornerRadius(20)
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(AppConstants.Typography.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Arrow
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textTertiary)
            }
            .padding(16)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .shadow(color: AppConstants.Colors.shadow, radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ContactInfoView()
}
