//
//  StudentCourseCard.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 29/7/25.
//

import SwiftUI

// MARK: - Student Course Card
struct StudentCourseCard: View {
    let course: StudentCourse
    let onDetailTap: (() -> Void)?

    init(course: StudentCourse, onDetailTap: (() -> Void)? = nil) {
        self.course = course
        self.onDetailTap = onDetailTap
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header with status and price
            headerSection
            
            // Course info
            courseInfoSection
            
            // Progress section - HIDDEN
            // if course.isCurrent || course.isCompleted {
            //     progressSection
            // }

            // Action buttons - HIDDEN
            // actionButtonsSection
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.3), lineWidth: 0.5)
        )
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Status badge
            statusBadge
            
            Spacer()
            
            // Price
            priceSection
        }
        .padding(.horizontal, 12)
        .padding(.top, 12)
    }
    
    private var statusBadge: some View {
        Text(course.statusText)
            .font(.beVietnamPro(.medium, size: 12))
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(statusColor)
            )
    }
    
    private var statusColor: Color {
        switch course.statusColor {
        case "green": return AppConstants.Colors.primary
        case "blue": return AppConstants.Colors.primary
        case "red": return AppConstants.Colors.error
        case "orange": return AppConstants.Colors.warning
        default: return .gray
        }
    }
    
    private var priceSection: some View {
        VStack(alignment: .trailing, spacing: 2) {
            if course.hasDiscount, let originalPrice = course.formattedOriginalPrice {
                Text(originalPrice)
                    .font(.beVietnamPro(.medium, size: 12))
                    .foregroundColor(.gray)
                    .strikethrough()
            }
            
            Text(course.formattedPrice)
                .font(.beVietnamPro(.bold, size: 14))
                .foregroundColor(course.hasDiscount ? .red : AppConstants.Colors.textPrimary)
        }
    }
    
    // MARK: - Course Info Section
    private var courseInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Course name
            Text(course.name)
                .font(.beVietnamPro(.bold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
                .lineLimit(2)

            // Course code and level
            HStack {
                Text("Mã: \(course.code)")
                    .font(.beVietnamPro(.medium, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)

                if let level = course.level {
                    Spacer()
                    levelBadge(level)
                }
            }

            // Short description
            if let shortDescription = course.shortDescription {
                Text(shortDescription)
                    .font(.beVietnamPro(.regular, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .lineLimit(2)
            }

            // Course details
            VStack(alignment: .leading, spacing: 8) {
                if let category = course.category {
                    courseDetailRow(icon: "folder", text: category)
                }

                if let subject = course.subjectName {
                    courseDetailRow(icon: "book", text: subject)
                }

                if let duration = course.durationHours {
                    courseDetailRow(icon: "clock", text: "\(Int(duration)) giờ")
                }

                if course.hasClass, let className = course.className {
                    courseDetailRow(icon: "graduationcap", text: className)
                }

                // Enrollment date
                courseDetailRow(icon: "calendar", text: "Đăng ký: \(formattedEnrollmentDate)")

                // Payment status
                paymentStatusRow
            }
        }
        .padding(.horizontal, 12)
        .padding(.top, 12)
        .padding(.bottom, 12)
    }
    
    private func courseDetailRow(icon: String, text: String) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 12))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .frame(width: 16)
            
            Text(text)
                .font(.beVietnamPro(.medium, size: 13))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
    
    // MARK: - Progress Section
    private var progressSection: some View {
        VStack(spacing: 12) {
            Divider()
                .padding(.horizontal, 12)
            
            HStack(spacing: 20) {
                // Progress
                if let progress = course.progressPercentage {
                    progressItem(
                        title: "Tiến độ",
                        value: course.progressText,
                        progress: progress / 100.0
                    )
                }
                
                // Attendance
                if let attendance = course.attendanceRate {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Điểm danh")
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(course.attendanceText)
                            .font(.beVietnamPro(.bold, size: 14))
                            .foregroundColor(attendance >= 80 ? AppConstants.Colors.primary : AppConstants.Colors.warning)
                    }
                }
                
                // Grade
                if let grade = course.currentGrade {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Điểm")
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(grade)
                            .font(.beVietnamPro(.bold, size: 14))
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                Spacer()
            }
            .padding(.horizontal, 12)
        }
    }
    
    private func progressItem(title: String, value: String, progress: Double) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            HStack(spacing: 8) {
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: AppConstants.Colors.primary))
                    .frame(width: 60)
                
                Text(value)
                    .font(.beVietnamPro(.bold, size: 12))
                    .foregroundColor(AppConstants.Colors.primary)
            }
        }
    }
    
    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 0) {
            Divider()
                .padding(.horizontal, 12)
            
            HStack(spacing: 12) {
                // Primary action button
                Button(action: {
                    // Handle primary action
                    print("Primary action for course: \(course.name)")
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: primaryActionIcon)
                            .font(.system(size: 14, weight: .medium))
                        
                        Text(primaryActionText)
                            .font(.beVietnamPro(.medium, size: 14))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(course.canAccess ? AppConstants.Colors.primary : .gray)
                    )
                }
                .disabled(!course.canAccess)
                .buttonStyle(PlainButtonStyle())
                
                Spacer()
                
                // Secondary action button
                Button(action: {
                    // Handle secondary action
                    print("Secondary action for course: \(course.name)")
                    onDetailTap?()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "info.circle")
                            .font(.system(size: 14))
                        
                        Text("Chi tiết")
                            .font(.beVietnamPro(.medium, size: 14))
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(AppConstants.Colors.primary, lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 12)
        }
    }
    
    private var primaryActionIcon: String {
        if course.isCompleted {
            return "checkmark.circle"
        } else if course.isCurrent {
            return "play.circle"
        } else {
            return "book.circle"
        }
    }
    
    private var primaryActionText: String {
        if course.isCompleted {
            return "Xem lại"
        } else if course.isCurrent {
            return "Tiếp tục học"
        } else {
            return "Bắt đầu học"
        }
    }

    // MARK: - Helper Methods
    private func levelBadge(_ level: String) -> some View {
        Text(levelDisplayName(level))
            .font(.beVietnamPro(.medium, size: 10))
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(levelColor(level))
            )
    }

    private func levelDisplayName(_ level: String) -> String {
        switch level.lowercased() {
        case "beginner": return "Cơ bản"
        case "intermediate": return "Trung bình"
        case "advanced": return "Nâng cao"
        default: return level.capitalized
        }
    }

    private func levelColor(_ level: String) -> Color {
        switch level.lowercased() {
        case "beginner": return AppConstants.Colors.primary
        case "intermediate": return AppConstants.Colors.warning
        case "advanced": return AppConstants.Colors.error
        default: return .gray
        }
    }

    private var paymentStatusRow: some View {
        HStack {
            Image(systemName: "creditcard")
                .font(.system(size: 12))
                .foregroundColor(paymentStatusColor)

            Text("Thanh toán: \(course.paymentStatus.displayName)")
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(paymentStatusColor)
        }
    }

    private var paymentStatusColor: Color {
        switch course.paymentStatus {
        case .paid: return AppConstants.Colors.success
        case .pending: return AppConstants.Colors.warning
        case .overdue: return AppConstants.Colors.error
        case .cancelled: return .gray
        case .unknown: return .gray
        }
    }

    private var formattedEnrollmentDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"

        if let date = formatter.date(from: course.enrollmentDate) {
            formatter.dateFormat = "dd/MM/yyyy"
            return formatter.string(from: date)
        }

        return course.enrollmentDate
    }
}

// MARK: - Preview
struct StudentCourseCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            StudentCourseCard(course: sampleCourse1)
            StudentCourseCard(course: sampleCourse2)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }

    static let sampleCourse1 = StudentCourse(
        id: 1,
        name: "Lập trình iOS với Swift",
        code: "IOS_SWIFT_2024",
        description: "Khóa học toàn diện về phát triển ứng dụng iOS",
        shortDescription: "Lập trình iOS cơ bản đến nâng cao",
        level: "intermediate",
        durationHours: 120.0,
        subjectName: "Lập trình di động",
        category: "Công nghệ",
        originalPrice: 5000000.0,
        discountedPrice: 4500000.0,
        hasDiscount: true,
        currencyCode: "VND",
        enrollmentId: 101,
        enrollmentDate: "2024-06-01",
        enrollmentStatus: .enrolled,
        paymentStatus: .paid,
        hasClass: true,
        classId: 201,
        className: "Lớp iOS Swift K24",
        classStatus: "active",
        progressPercentage: 65.5,
        attendanceRate: 92.0,
        currentGrade: "A",
        isCurrent: true,
        isCompleted: false,
        canAccess: true,
        needsAttention: false,
        attentionReason: nil,
        createdAt: "2024-06-01T08:00:00+07:00",
        updatedAt: "2024-07-29T10:30:00+07:00"
    )

    static let sampleCourse2 = StudentCourse(
        id: 2,
        name: "Tiếng Anh giao tiếp",
        code: "ENG_COMM_2024",
        description: "Khóa học phát triển kỹ năng giao tiếp tiếng Anh",
        shortDescription: "Tiếng Anh giao tiếp công việc",
        level: "beginner",
        durationHours: 80.0,
        subjectName: "Ngoại ngữ",
        category: "Ngôn ngữ",
        originalPrice: 3000000.0,
        discountedPrice: nil,
        hasDiscount: false,
        currencyCode: "VND",
        enrollmentId: 102,
        enrollmentDate: "2024-05-15",
        enrollmentStatus: .completed,
        paymentStatus: .paid,
        hasClass: true,
        classId: 202,
        className: "Lớp Tiếng Anh B1",
        classStatus: "completed",
        progressPercentage: 100.0,
        attendanceRate: 95.5,
        currentGrade: "B+",
        isCurrent: false,
        isCompleted: true,
        canAccess: true,
        needsAttention: false,
        attentionReason: nil,
        createdAt: "2024-05-15T09:00:00+07:00",
        updatedAt: "2024-07-20T16:45:00+07:00"
    )
}
