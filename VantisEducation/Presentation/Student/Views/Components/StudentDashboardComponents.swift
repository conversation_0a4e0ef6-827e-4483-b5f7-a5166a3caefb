import SwiftUI

// MARK: - Upcoming Task Card
struct UpcomingTaskCard: View {
    let title: String
    let subtitle: String
    let dueDate: Date
    let type: TaskType
    let isOverdue: Bool
    
    enum TaskType {
        case assignment, exam, quiz
        
        var icon: String {
            switch self {
            case .assignment: return "doc.text.fill"
            case .exam: return "graduationcap.fill"
            case .quiz: return "questionmark.circle.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .assignment: return .blue
            case .exam: return .red
            case .quiz: return .orange
            }
        }
    }
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: type.icon)
                .font(.title3)
                .foregroundColor(isOverdue ? .red : type.color)
                .frame(width: 32, height: 32)
                .background(isOverdue ? Color.red.opacity(0.1) : type.color.opacity(0.1))
                .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .lineLimit(2)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Text(dueDateString)
                    .font(.caption)
                    .foregroundColor(isOverdue ? AppConstants.Colors.error : AppConstants.Colors.textSecondary)
            }
            
            Spacer()
            
            if isOverdue {
                Text("Quá hạn")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppConstants.Colors.error)
                    .cornerRadius(4)
            }
        }
        .padding(12)
        .background(AppConstants.Colors.surface)
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isOverdue ? Color.red.opacity(0.3) : Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
    
    private var dueDateString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: dueDate, relativeTo: Date())
    }
}

// MARK: - Today Class Card
struct TodayClassCard: View {
    let classItem: Class
    
    var body: some View {
        HStack(spacing: 12) {
            VStack(spacing: 4) {
                Text(timeString(from: classItem.startTime))
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(timeString(from: classItem.endTime))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(width: 50)
            
            Rectangle()
                .fill(AppConstants.Colors.primary)
                .frame(width: 3)
                .cornerRadius(1.5)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(classItem.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                if let location = classItem.location {
                    Text("Phòng: \(location.displayName)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Text("GV: \(classItem.instructorName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if classItem.startTime > Date() {
                Text("Sắp tới")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppConstants.Colors.primary.opacity(0.1))
                    .cornerRadius(4)
            }
        }
        .padding(12)
        .background(AppConstants.Colors.surface)
        .cornerRadius(8)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private func timeString(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Dashboard Empty State View
struct DashboardEmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color

    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.largeTitle)
                .foregroundColor(color)

            VStack(spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 32)
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
    }
}

// MARK: - Progress Ring
struct ProgressRing: View {
    let progress: Double
    let lineWidth: CGFloat
    let size: CGFloat
    let color: Color
    
    init(progress: Double, lineWidth: CGFloat = 8, size: CGFloat = 60, color: Color = .blue) {
        self.progress = progress
        self.lineWidth = lineWidth
        self.size = size
        self.color = color
    }
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.2), lineWidth: lineWidth)
            
            Circle()
                .trim(from: 0, to: progress / 100)
                .stroke(
                    color,
                    style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1), value: progress)
            
            Text("\(Int(progress))%")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
        }
        .frame(width: size, height: size)
    }
}

// MARK: - Grade Badge
struct GradeBadge: View {
    let grade: Double
    let maxGrade: Double
    
    private var gradeColor: Color {
        let percentage = grade / maxGrade
        switch percentage {
        case 0.9...: return .green
        case 0.8..<0.9: return .blue
        case 0.7..<0.8: return .orange
        case 0.6..<0.7: return .yellow
        default: return .red
        }
    }
    
    private var gradeText: String {
        return String(format: "%.1f", grade)
    }
    
    var body: some View {
        Text(gradeText)
            .font(.caption)
            .fontWeight(.bold)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(gradeColor)
            .cornerRadius(6)
    }
}

// MARK: - Notification Dot
struct NotificationDot: View {
    let count: Int
    let color: Color
    
    init(count: Int, color: Color = .red) {
        self.count = count
        self.color = color
    }
    
    var body: some View {
        if count > 0 {
            Text(count > 99 ? "99+" : "\(count)")
                .font(.caption2)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .padding(.horizontal, count > 9 ? 6 : 4)
                .padding(.vertical, 2)
                .background(color)
                .cornerRadius(10)
                .scaleEffect(count > 0 ? 1 : 0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: count)
        }
    }
}

// MARK: - Study Streak Card
struct StudyStreakCard: View {
    let currentStreak: Int
    let longestStreak: Int
    
    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 4) {
                Text("Chuỗi học tập")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Text("\(currentStreak) ngày")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("Kỷ lục")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Text("\(longestStreak) ngày")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.warning)
            }
            
            Image(systemName: "flame.fill")
                .font(.title2)
                .foregroundColor(AppConstants.Colors.warning)
        }
        .padding(16)
        .background(
            LinearGradient(
                colors: [AppConstants.Colors.warning.opacity(0.1), AppConstants.Colors.error.opacity(0.1)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }
}
