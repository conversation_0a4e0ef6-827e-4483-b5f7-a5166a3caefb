//
//  StudentCourseDetailView.swift
//  VantisEducation
//
//  Created by Student App on 31/7/25.
//

import SwiftUI

// MARK: - Student Course Detail View
struct StudentCourseDetailView: View {
    let courseId: Int
    @StateObject private var viewModel: StudentCourseDetailViewModel
    
    init(courseId: Int) {
        self.courseId = courseId
        self._viewModel = StateObject(wrappedValue: StudentCourseDetailViewModel(courseId: courseId))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            if viewModel.isLoading {
                loadingView
            } else if let course = viewModel.courseDetail {
                courseDetailContent(course: course)
            } else {
                errorView
            }
        }
        .navigationTitle("Chi tiết khóa học")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(false)
        .onAppear {
            viewModel.loadCourseDetail()
        }
        .alert("Lỗi", isPresented: $viewModel.showError) {
            Button("Thử lại") {
                viewModel.refreshCourseDetail()
            }
            But<PERSON>("Đóng", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage ?? "Đã xảy ra lỗi không xác định")
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Đang tải thông tin khóa học...")
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Error View
    private var errorView: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("Không thể tải thông tin khóa học")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(viewModel.errorMessage ?? "Đã xảy ra lỗi không xác định")
                .font(.beVietnamPro(.regular, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
            
            Button("Thử lại") {
                viewModel.refreshCourseDetail()
            }
            .font(.beVietnamPro(.medium, size: 16))
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(AppConstants.Colors.primary)
            .cornerRadius(8)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Course Detail Content
    private func courseDetailContent(course: StudentCourse) -> some View {
        ScrollView {
            VStack(spacing: 20) {
                // Header Section
                courseHeaderSection(course: course)
                
                // Status and Progress Section
                statusProgressSection(course: course)
                
                // Course Information Section
                courseInfoSection(course: course)
                
                // Class Information Section (if available)
                if course.hasClass {
                    classInfoSection(course: course)
                }
                
                // Payment Information Section
                paymentInfoSection(course: course)
                
                // Action Buttons Section
                actionButtonsSection(course: course)
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
        }
        .background(AppConstants.Colors.background)
        .refreshable {
            viewModel.refreshCourseDetail()
        }
    }
    
    // MARK: - Header Section
    private func courseHeaderSection(course: StudentCourse) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // Course name
            Text(course.name)
                .font(.beVietnamPro(.bold, size: 24))
                .foregroundColor(AppConstants.Colors.textPrimary)
                .multilineTextAlignment(.leading)
            
            // Course code
            Text(course.code)
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            // Course description
            if let description = course.description, !description.isEmpty {
                Text(description)
                    .font(.beVietnamPro(.regular, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .lineLimit(nil)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    // MARK: - Status and Progress Section
    private func statusProgressSection(course: StudentCourse) -> some View {
        VStack(spacing: 16) {
            HStack(spacing: 16) {
                // Status
                VStack(alignment: .leading, spacing: 4) {
                    Text("Trạng thái")
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Text(viewModel.getCourseStatusText())
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(viewModel.getCourseStatusColor())
                }
                
                Spacer()
                
                // Progress
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Tiến độ")
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Text(viewModel.getProgressText())
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(AppConstants.Colors.primary)
                }
            }
            
            // Progress bar
            if let progress = course.progressPercentage {
                ProgressView(value: progress / 100.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: AppConstants.Colors.primary))
                    .scaleEffect(x: 1, y: 2, anchor: .center)
            }
            
            // Attendance
            if let attendance = course.attendanceRate {
                HStack {
                    Text("Tỷ lệ tham gia:")
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    Text(viewModel.getAttendanceText())
                        .font(.beVietnamPro(.semiBold, size: 14))
                        .foregroundColor(attendance >= 80 ? .green : attendance >= 60 ? .orange : .red)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Course Information Section
    private func courseInfoSection(course: StudentCourse) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Thông tin khóa học")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: 8) {
                if let category = course.category {
                    courseInfoRow(icon: "folder", title: "Danh mục", value: category)
                }

                if let subject = course.subjectName {
                    courseInfoRow(icon: "book", title: "Môn học", value: subject)
                }

                if let duration = course.durationHours {
                    courseInfoRow(icon: "clock", title: "Thời lượng", value: "\(Int(duration)) giờ")
                }

                if let level = course.level {
                    courseInfoRow(icon: "chart.bar", title: "Cấp độ", value: level.capitalized)
                }

                courseInfoRow(icon: "calendar", title: "Ngày đăng ký", value: formatEnrollmentDate(course.enrollmentDate))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Class Information Section
    private func classInfoSection(course: StudentCourse) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Thông tin lớp học")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: 8) {
                if let className = course.className {
                    courseInfoRow(icon: "graduationcap", title: "Tên lớp", value: className)
                }

                if let classStatus = course.classStatus {
                    courseInfoRow(icon: "info.circle", title: "Trạng thái lớp", value: classStatus.capitalized)
                }

                if let grade = course.currentGrade {
                    courseInfoRow(icon: "star", title: "Điểm hiện tại", value: grade)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Payment Information Section
    private func paymentInfoSection(course: StudentCourse) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Thông tin thanh toán")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: 8) {
                // Payment status
                HStack {
                    Image(systemName: "creditcard")
                        .font(.system(size: 16))
                        .foregroundColor(AppConstants.Colors.primary)
                        .frame(width: 24)

                    Text("Trạng thái thanh toán")
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Spacer()

                    Text(course.paymentStatus.displayName)
                        .font(.beVietnamPro(.semiBold, size: 14))
                        .foregroundColor(course.paymentStatus == .paid ? .green : .orange)
                }

                // Price information
                if let originalPrice = course.originalPrice {
                    HStack {
                        Image(systemName: "tag")
                            .font(.system(size: 16))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24)

                        Text("Giá gốc")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Spacer()

                        Text(formatPrice(originalPrice, currency: course.currencyCode))
                            .font(.beVietnamPro(.semiBold, size: 14))
                            .foregroundColor(course.hasDiscount ? AppConstants.Colors.textSecondary : AppConstants.Colors.textPrimary)
                            .strikethrough(course.hasDiscount)
                    }
                }

                if course.hasDiscount, let discountedPrice = course.discountedPrice {
                    HStack {
                        Image(systemName: "tag.fill")
                            .font(.system(size: 16))
                            .foregroundColor(.red)
                            .frame(width: 24)

                        Text("Giá khuyến mãi")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Spacer()

                        Text(formatPrice(discountedPrice, currency: course.currencyCode))
                            .font(.beVietnamPro(.bold, size: 14))
                            .foregroundColor(.red)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Action Buttons Section
    private func actionButtonsSection(course: StudentCourse) -> some View {
        VStack(spacing: 12) {
            if viewModel.canAccessCourse() {
                Button(action: {
                    // Handle access course action
                    print("🎯 Access course: \(course.name)")
                }) {
                    HStack {
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 20))

                        Text("Vào học")
                            .font(.beVietnamPro(.semiBold, size: 16))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(AppConstants.Colors.primary)
                    .cornerRadius(12)
                }
            }

            Button(action: {
                // Handle view schedule action
                print("🎯 View schedule for course: \(course.name)")
            }) {
                HStack {
                    Image(systemName: "calendar")
                        .font(.system(size: 20))

                    Text("Xem lịch học")
                        .font(.beVietnamPro(.semiBold, size: 16))
                }
                .foregroundColor(AppConstants.Colors.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppConstants.Colors.primary, lineWidth: 2)
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Helper Views
    private func courseInfoRow(icon: String, title: String, value: String) -> some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 24)

            Text(title)
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)

            Spacer()

            Text(value)
                .font(.beVietnamPro(.semiBold, size: 14))
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
    }

    // MARK: - Helper Methods
    private func formatEnrollmentDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"

        if let date = formatter.date(from: dateString) {
            formatter.dateFormat = "dd/MM/yyyy"
            return formatter.string(from: date)
        }

        return dateString
    }

    private func formatPrice(_ price: Double, currency: String?) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.groupingSeparator = "."

        let formattedPrice = formatter.string(from: NSNumber(value: price)) ?? "\(Int(price))"

        if let currency = currency, currency.uppercased() == "VND" {
            return "\(formattedPrice) ₫"
        } else {
            return "\(formattedPrice) \(currency ?? "")"
        }
    }
}
