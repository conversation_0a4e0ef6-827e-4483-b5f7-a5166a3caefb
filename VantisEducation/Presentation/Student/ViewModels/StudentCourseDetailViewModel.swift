//
//  StudentCourseDetailViewModel.swift
//  VantisEducation
//
//  Created by Student App on 31/7/25.
//

import SwiftUI
import Combine

// MARK: - Student Course Detail View Model
@MainActor
class StudentCourseDetailViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var courseDetail: StudentCourse?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showError = false
    
    // MARK: - Private Properties
    private let coursesService: StudentCoursesService
    private var cancellables = Set<AnyCancellable>()
    private let courseId: Int
    
    // MARK: - Initialization
    init(courseId: Int, coursesService: StudentCoursesService = StudentCoursesService()) {
        self.courseId = courseId
        self.coursesService = coursesService
    }
    
    // MARK: - Public Methods
    func loadCourseDetail() {
        guard !isLoading else { return }
        
        isLoading = true
        errorMessage = nil
        showError = false
        
        print("🎯 StudentCourseDetailViewModel: Loading course detail for ID: \(courseId)")
        
        coursesService.getCourseDetail(courseId: courseId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completion {
                        print("❌ StudentCourseDetailViewModel: Failed to load course detail: \(error)")
                        self?.errorMessage = self?.getErrorMessage(from: error)
                        self?.showError = true
                    }
                },
                receiveValue: { [weak self] courseDetail in
                    print("✅ StudentCourseDetailViewModel: Successfully loaded course detail")
                    print("📋 Course Detail: \(courseDetail.name) (ID: \(courseDetail.id))")
                    self?.courseDetail = courseDetail
                }
            )
            .store(in: &cancellables)
    }
    
    func refreshCourseDetail() {
        loadCourseDetail()
    }
    
    // MARK: - Helper Methods
    private func getErrorMessage(from error: Error) -> String {
        if let apiError = error as? StudentCoursesAPIError {
            switch apiError {
            case .networkError(let message):
                return "Lỗi kết nối mạng: \(message)"
            case .decodingError:
                return "Lỗi xử lý dữ liệu từ server."
            case .unauthorized:
                return "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."
            case .noData:
                return "Không có dữ liệu từ server."
            case .invalidURL:
                return "Lỗi hệ thống. Vui lòng thử lại sau."
            }
        }

        return "Đã xảy ra lỗi không xác định. Vui lòng thử lại."
    }
    
    // MARK: - Course Actions
    func canAccessCourse() -> Bool {
        guard let course = courseDetail else { return false }
        return course.canAccess && course.paymentStatus == .paid
    }
    
    func getCourseStatusText() -> String {
        guard let course = courseDetail else { return "Không xác định" }
        
        if course.isCompleted {
            return "Đã hoàn thành"
        } else if course.isCurrent {
            return "Đang học"
        } else {
            return course.enrollmentStatus.displayName
        }
    }
    
    func getCourseStatusColor() -> Color {
        guard let course = courseDetail else { return .gray }
        
        if course.isCompleted {
            return .green
        } else if course.isCurrent {
            return AppConstants.Colors.primary
        } else {
            return .orange
        }
    }
    
    func getProgressText() -> String {
        guard let course = courseDetail,
              let progress = course.progressPercentage else {
            return "Chưa có tiến độ"
        }
        
        return String(format: "%.1f%%", progress)
    }
    
    func getAttendanceText() -> String {
        guard let course = courseDetail,
              let attendance = course.attendanceRate else {
            return "Chưa có dữ liệu"
        }
        
        return String(format: "%.1f%%", attendance)
    }
}
