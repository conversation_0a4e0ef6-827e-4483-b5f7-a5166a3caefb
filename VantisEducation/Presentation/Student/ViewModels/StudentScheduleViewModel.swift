//
//  StudentScheduleViewModel.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import Foundation
import SwiftUI
import Combine
import CoreLocation

@MainActor
class StudentScheduleViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var lessons: [StudentLesson] = []
    @Published var calendarEvents: [CalendarEvent] = []
    @Published var isLoading = false
    @Published var error: String?
    @Published var isRefreshing = false
    @Published var selectedLesson: StudentLesson?
    @Published var showDetailBottomSheet = false
    @Published var showCheckinConfirmation = false
    @Published var isProcessingAttendance = false
    @Published var showAttendanceResult = false
    @Published var attendanceResult: AttendanceResult?

    // Calendar specific properties
    @Published var selectedDate = Date()
    @Published var viewType: ScheduleViewType = .week
    @Published var activeTab: ScheduleTab = .schedules
    @Published var currentFilter = CalendarFilter.empty

    // MARK: - Private Properties
    private let lessonsService: StudentLessonsService
    private let scheduleService: StudentScheduleService
    private let locationManager = CLLocationManager()
    private var locationDelegate: LocationDelegate?

    // MARK: - Initialization
    init(
        lessonsService: StudentLessonsService = StudentLessonsService(),
        scheduleService: StudentScheduleService = StudentScheduleService()
    ) {
        self.lessonsService = lessonsService
        self.scheduleService = scheduleService
        setupObservers()
    }

    // MARK: - Public Methods

    func loadLessons() async {
        guard !isLoading else { return }

        print("🔄 StudentScheduleViewModel: Starting to load lessons...")
        isLoading = true
        error = nil

        do {
            print("🔍 StudentScheduleViewModel: Calling API endpoint: /students/lessons/")
            print("🔍 StudentScheduleViewModel: Parameters - page: 1, pageSize: 50, classId: nil")

            let response = try await lessonsService.getStudentLessons(page: 1, pageSize: 50, classId: nil)

            print("📦 StudentScheduleViewModel: Received response - success: \(response.success), data count: \(response.data.count)")
            print("📦 StudentScheduleViewModel: Response message: \(response.message)")

            if response.success {
                lessons = response.data
                print("✅ StudentScheduleViewModel: Lessons loaded - count: \(lessons.count)")
                // Log first few lessons for debugging
                for (index, lesson) in lessons.prefix(3).enumerated() {
                    print("📚 Lesson \(index + 1): \(lesson.name) - isToday: \(lesson.isToday), isUpcoming: \(lesson.isUpcoming), isLive: \(lesson.isLive)")
                }
            } else {
                error = response.message
                print("❌ StudentScheduleViewModel: Response failed - \(response.message)")
            }
        } catch {
            self.error = error.localizedDescription
            print("❌ StudentScheduleViewModel: Failed to load lessons - \(error)")
            print("❌ StudentScheduleViewModel: Error details: \(String(describing: error))")
        }

        isLoading = false
        print("🏁 StudentScheduleViewModel: Load lessons completed - isLoading: \(isLoading), lessons count: \(lessons.count)")
    }

    func refreshLessons() async {
        guard !isRefreshing else { return }
        
        isRefreshing = true
        error = nil
        
        do {
            let response = try await lessonsService.getStudentLessons(page: 1, pageSize: 50, classId: nil)

            if response.success {
                lessons = response.data
                print("🔄 StudentScheduleViewModel: Refreshed lessons - count: \(lessons.count)")
            } else {
                error = response.message
            }
        } catch {
            self.error = error.localizedDescription
            print("❌ StudentScheduleViewModel: Failed to refresh lessons - \(error)")
        }
        
        isRefreshing = false
    }

    // MARK: - Calendar Methods

    func loadCalendarEvents() async {
        guard !isLoading else { return }

        isLoading = true
        error = nil

        let request = ScheduleRequest(
            view: viewType,
            dateFrom: getDateFrom(),
            dateTo: getDateTo(),
            courseId: currentFilter.courseId,
            classId: currentFilter.classId,
            page: 1,
            pageSize: 100
        )

        do {
            let events = try await scheduleService.getCalendarEvents(request: request)
            calendarEvents = filterEvents(events)
            print("✅ StudentScheduleViewModel: Calendar events loaded - count: \(calendarEvents.count)")
        } catch {
            self.error = error.localizedDescription
            print("❌ StudentScheduleViewModel: Failed to load calendar events - \(error)")
        }

        isLoading = false
    }

    func refreshCalendarEvents() async {
        guard !isRefreshing else { return }

        isRefreshing = true
        error = nil

        let request = ScheduleRequest(
            view: viewType,
            dateFrom: getDateFrom(),
            dateTo: getDateTo(),
            courseId: currentFilter.courseId,
            classId: currentFilter.classId,
            page: 1,
            pageSize: 100
        )

        do {
            let events = try await scheduleService.getCalendarEvents(request: request)
            calendarEvents = filterEvents(events)
            print("🔄 StudentScheduleViewModel: Calendar events refreshed - count: \(calendarEvents.count)")
        } catch {
            self.error = error.localizedDescription
            print("❌ StudentScheduleViewModel: Failed to refresh calendar events - \(error)")
        }

        isRefreshing = false
    }

    func changeViewType(_ newViewType: ScheduleViewType) {
        viewType = newViewType
        Task {
            await loadCalendarEvents()
        }
    }

    func changeSelectedDate(_ newDate: Date) {
        selectedDate = newDate
        Task {
            await loadCalendarEvents()
        }
    }

    func selectTab(_ tab: ScheduleTab) {
        activeTab = tab

        // Load appropriate data based on tab
        Task {
            switch tab {
            case .schedules:
                await loadLessons()
            case .calendar:
                await loadCalendarEvents()
            }
        }
    }

    func selectLesson(_ lesson: StudentLesson) {
        selectedLesson = lesson
        showDetailBottomSheet = true
    }

    func selectCalendarEvent(_ event: CalendarEvent) {
        selectedLesson = event.originalLesson
        showDetailBottomSheet = true
    }

    func applyFilter(_ filter: CalendarFilter) {
        currentFilter = filter
        Task {
            await loadCalendarEvents()
        }
    }

    // MARK: - Helper Methods

    private func getDateFrom() -> Date? {
        let calendar = Calendar.current

        switch viewType {
        case .day:
            return selectedDate
        case .week:
            return calendar.dateInterval(of: .weekOfYear, for: selectedDate)?.start
        case .month:
            return calendar.dateInterval(of: .month, for: selectedDate)?.start
        }
    }

    private func getDateTo() -> Date? {
        let calendar = Calendar.current

        switch viewType {
        case .day:
            return selectedDate
        case .week:
            return calendar.dateInterval(of: .weekOfYear, for: selectedDate)?.end
        case .month:
            return calendar.dateInterval(of: .month, for: selectedDate)?.end
        }
    }

    private func filterEvents(_ events: [CalendarEvent]) -> [CalendarEvent] {
        return events.filter { event in
            // Filter by search text
            if !currentFilter.searchText.isEmpty {
                let searchText = currentFilter.searchText.lowercased()
                let matchesTitle = event.title.lowercased().contains(searchText)
                let matchesSubtitle = event.subtitle.lowercased().contains(searchText)
                let matchesInstructor = event.instructor.lowercased().contains(searchText)

                if !matchesTitle && !matchesSubtitle && !matchesInstructor {
                    return false
                }
            }

            // Filter by lesson type/status
            switch event.lessonType {
            case .live:
                return currentFilter.showLive
            case .today:
                return currentFilter.showToday
            case .upcoming:
                return currentFilter.showUpcoming
            case .completed:
                return currentFilter.showCompleted
            default:
                return true
            }
        }
    }

    func closeDetailBottomSheet() {
        showDetailBottomSheet = false
        selectedLesson = nil
    }

    func showCheckinConfirmation(for lesson: StudentLesson) {
        selectedLesson = lesson
        showCheckinConfirmation = true
    }
    
    func closeCheckinConfirmation() {
        showCheckinConfirmation = false
    }
    
    func confirmCheckin() async {
        guard let lesson = selectedLesson else {
            print("❌ confirmCheckin: No selected lesson")
            return
        }

        print("🎯 confirmCheckin: Starting checkin for lesson \(lesson.id)")

        // Debug: Check if we have token
        let tokenManager = SecureTokenManager.shared
        if let token = tokenManager.getToken() {
            print("🔐 confirmCheckin: Token available (length: \(token.count))")
            print("🔐 confirmCheckin: Token preview: \(String(token.prefix(50)))...")
        } else {
            print("❌ confirmCheckin: NO TOKEN AVAILABLE!")
        }

        showCheckinConfirmation = false
        isProcessingAttendance = true

        do {
            print("🔍 confirmCheckin: Getting current location...")
            let location = try await getCurrentLocation()
            print("✅ confirmCheckin: Location obtained: \(location.latitude), \(location.longitude)")

            let deviceInfo = LessonAdapter.getCurrentDeviceInfo()
            print("📱 confirmCheckin: Device info: \(deviceInfo)")

            print("🌐 confirmCheckin: Calling checkin API...")
            let result = await lessonsService.checkinLesson(
                lessonId: lesson.id,
                latitude: location.latitude,
                longitude: location.longitude,
                deviceInfo: deviceInfo,
                notes: "Điểm danh từ ứng dụng"
            )

            print("📡 confirmCheckin: API result received - \(result)")
            NSLog("📡 confirmCheckin: API result received - %@", String(describing: result))

            switch result {
            case .success(let response):
                attendanceResult = AttendanceResult(
                    type: .success,
                    message: response.data?.status.displayMessage ?? "Điểm danh thành công!",
                    lessonId: String(lesson.id)
                )

                // Update lesson attendance status
                updateLessonAttendance(lessonId: lesson.id, status: StudentAttendanceStatus.present)
                print("✅ confirmCheckin: Checkin successful")

            case .alreadyCheckedIn(let message):
                attendanceResult = AttendanceResult(
                    type: .alreadyCheckedIn,
                    message: message,
                    lessonId: String(lesson.id)
                )
                // Update UI to show already checked in
                updateLessonAttendance(lessonId: lesson.id, status: StudentAttendanceStatus.present)
                print("⚠️ confirmCheckin: Already checked in - \(message)")

            case .lessonNotFound(let message):
                attendanceResult = AttendanceResult(
                    type: .failure,
                    message: message,
                    lessonId: String(lesson.id)
                )
                print("❌ confirmCheckin: Lesson not found - \(message)")

            case .unauthorized:
                attendanceResult = AttendanceResult(
                    type: .failure,
                    message: result.userMessage,
                    lessonId: String(lesson.id)
                )
                // Handle logout
                print("🔐 confirmCheckin: Unauthorized - need to logout")

            case .validationError(let message),
                 .networkError(let message),
                 .serverError(let message),
                 .unknownError(let message):
                attendanceResult = AttendanceResult(
                    type: .failure,
                    message: message,
                    lessonId: String(lesson.id)
                )
                print("❌ confirmCheckin: Error - \(message)")
            }

        } catch {
            print("❌ confirmCheckin: Exception caught - \(error)")
            NSLog("❌ confirmCheckin: Exception caught - %@", error.localizedDescription)

            // This catch block now only handles location errors
            let errorMessage: String
            if error.localizedDescription.contains("location") {
                errorMessage = "Không thể lấy vị trí. Vui lòng kiểm tra quyền truy cập vị trí."
            } else {
                errorMessage = "DEBUG: \(error.localizedDescription)"
            }

            attendanceResult = AttendanceResult(
                type: .failure,
                message: errorMessage,
                lessonId: String(lesson.id)
            )
        }

        isProcessingAttendance = false
        showAttendanceResult = true
        print("🏁 confirmCheckin: Process completed")
    }
    
    func closeAttendanceResult() {
        showAttendanceResult = false
        attendanceResult = nil
    }
    
    func retryLoadLessons() async {
        await loadLessons()
    }
    
    // MARK: - Private Methods
    
    private func setupLocationManager() {
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        requestLocationPermissionIfNeeded()
    }

    private func requestLocationPermissionIfNeeded() {
        let status = locationManager.authorizationStatus

        switch status {
        case .notDetermined:
            print("🔍 Requesting location permission...")
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            print("❌ Location permission denied or restricted")
        case .authorizedWhenInUse, .authorizedAlways:
            print("✅ Location permission already granted")
        @unknown default:
            print("⚠️ Unknown location permission status")
        }
    }
    
    private func setupObservers() {
        // Simplified - no complex filtering for now
        print("📋 StudentScheduleViewModel: Setup observers - simplified version")
    }
    
    private func processLessonsData() {
        print("🔄 StudentScheduleViewModel: Processing lessons data - input count: \(lessons.count)")
        // Simplified - just use lessons directly
        print("✅ StudentScheduleViewModel: Using lessons directly - count: \(lessons.count)")
    }
    
    private func applyFilters() {
        print("🎯 StudentScheduleViewModel: Simplified filtering - no complex filters")
        // Simplified - no filtering for now
    }
    
    private func getCurrentLocation() async throws -> CLLocationCoordinate2D {
        return try await withCheckedThrowingContinuation { continuation in
            // Check authorization status
            let status = locationManager.authorizationStatus
            print("🔍 getCurrentLocation: Authorization status: \(status)")

            guard status == .authorizedWhenInUse || status == .authorizedAlways else {
                print("❌ Location permission denied. Status: \(status)")
                continuation.resume(throwing: StudentLessonsError.locationPermissionDenied)
                return
            }

            print("🔍 Getting current location...")

            // Create a location delegate to handle the response and keep it alive
            locationDelegate = LocationDelegate { [weak self] result in
                switch result {
                case .success(let location):
                    print("✅ Location obtained: \(location.coordinate.latitude), \(location.coordinate.longitude)")
                    continuation.resume(returning: location.coordinate)
                case .failure(let error):
                    print("❌ Location error: \(error)")
                    // Fallback to mock location for demo
                    let mockLocation = CLLocationCoordinate2D(latitude: 21.0285, longitude: 105.8542)
                    print("🎯 Using mock location: \(mockLocation.latitude), \(mockLocation.longitude)")
                    continuation.resume(returning: mockLocation)
                }
                // Clear delegate after use
                self?.locationDelegate = nil
            }

            // Set delegate and request location
            locationManager.delegate = locationDelegate
            locationManager.requestLocation()

            // Add timeout to prevent infinite waiting
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
                if self.locationDelegate != nil {
                    print("⏰ Location request timeout, using mock location")
                    let mockLocation = CLLocationCoordinate2D(latitude: 21.0285, longitude: 105.8542)
                    continuation.resume(returning: mockLocation)
                    self.locationDelegate = nil
                }
            }
        }
    }
    
    private func updateLessonAttendance(lessonId: Int, status: StudentAttendanceStatus) {
        // Update in lessons array
        if let index = lessons.firstIndex(where: { $0.id == lessonId }) {
            lessons[index] = StudentLesson(
                id: lessons[index].id,
                name: lessons[index].name,
                className: lessons[index].className,
                subjectName: lessons[index].subjectName,
                instructorName: lessons[index].instructorName,
                startDatetime: lessons[index].startDatetime,
                endDatetime: lessons[index].endDatetime,
                durationHours: lessons[index].durationHours,
                room: lessons[index].room,
                location: lessons[index].location,
                attendanceStatus: status,
                lessonStatus: lessons[index].lessonStatus,
                canAccess: lessons[index].canAccess,
                needsAttention: lessons[index].needsAttention,
                attentionReason: lessons[index].attentionReason,
                isToday: lessons[index].isToday,
                isUpcoming: lessons[index].isUpcoming,
                isLive: lessons[index].isLive,
                lessonNumber: lessons[index].lessonNumber,
                hasAssignments: lessons[index].hasAssignments,
                pendingAssignments: lessons[index].pendingAssignments,
                imageUrl: lessons[index].imageUrl
            )
        }
        
        // Reprocess data to update UI
        processLessonsData()
    }
}
// MARK: - Supporting Models
// AttendanceResult and AttendanceResultType are now defined in Domain/Models/AttendanceResult.swift

// MARK: - Preview Helper

extension StudentScheduleViewModel {
    static func preview() -> StudentScheduleViewModel {
        let viewModel = StudentScheduleViewModel()
        
        // Load mock data immediately for preview
        Task {
            await viewModel.loadLessons()
        }
        
        return viewModel
    }
}

// MARK: - LocationDelegate
class LocationDelegate: NSObject, CLLocationManagerDelegate {
    private let completion: (Result<CLLocation, Error>) -> Void

    init(completion: @escaping (Result<CLLocation, Error>) -> Void) {
        self.completion = completion
        super.init()
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.first else { return }
        completion(.success(location))
        manager.stopUpdatingLocation()
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        completion(.failure(error))
        manager.stopUpdatingLocation()
    }
}
