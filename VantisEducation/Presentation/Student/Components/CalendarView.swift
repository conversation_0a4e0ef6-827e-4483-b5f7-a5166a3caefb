//
//  CalendarView.swift
//  VantisEducation
//
//  Created by Mobile App Template on 31/7/25.
//

import SwiftUI

// MARK: - Day Calendar View

struct DayCalendarView: View {
    let events: [CalendarEvent]
    let selectedDate: Date
    let onEventTap: (CalendarEvent) -> Void

    private let hourHeight: CGFloat = 60
    private let hours = Array(0...23)

    var body: some View {
        ScrollView {
            ZStack(alignment: .topLeading) {
                // Hour grid
                VStack(spacing: 0) {
                    ForEach(hours, id: \.self) { hour in
                        HStack {
                            Text(String(format: "%02d:00", hour))
                                .font(.beVietnamPro(.medium, size: 12))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .frame(width: 50, alignment: .trailing)

                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 1)
                        }
                        .frame(height: hourHeight)
                    }
                }

                // Events
                ForEach(events) { event in
                    DayEventView(event: event, hourHeight: hourHeight) {
                        onEventTap(event)
                    }
                }
            }
        }
        .padding(.horizontal, 16)
    }
}

// MARK: - Day Event View

struct DayEventView: View {
    let event: CalendarEvent
    let hourHeight: CGFloat
    let onTap: () -> Void

    private var eventPosition: (top: CGFloat, height: CGFloat) {
        let calendar = Calendar.current
        let startHour = calendar.component(.hour, from: event.startDate)
        let startMinute = calendar.component(.minute, from: event.startDate)

        let top = CGFloat(startHour) * hourHeight + CGFloat(startMinute) * hourHeight / 60
        let duration = event.duration / 3600 // Convert to hours
        let height = max(CGFloat(duration) * hourHeight, 30) // Minimum height

        return (top, height)
    }

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 4) {
                Text(event.title)
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(.white)
                    .lineLimit(2)

                Text(event.subtitle)
                    .font(.beVietnamPro(.medium, size: 10))
                    .foregroundColor(.white.opacity(0.8))
                    .lineLimit(1)

                if let location = event.location {
                    Text(location)
                        .font(.beVietnamPro(.medium, size: 10))
                        .foregroundColor(.white.opacity(0.7))
                        .lineLimit(1)
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
            .frame(maxWidth: .infinity, alignment: .leading)
            .frame(height: eventPosition.height)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color(hex: event.courseColor))
            )
        }
        .offset(x: 60, y: eventPosition.top)
        .padding(.trailing, 16)
    }
}

// MARK: - Week Calendar View

struct WeekCalendarView: View {
    let events: [CalendarEvent]
    let selectedDate: Date
    let onEventTap: (CalendarEvent) -> Void
    let onDateTap: (Date) -> Void

    private var weekDays: [Date] {
        let calendar = Calendar.current
        guard let weekInterval = calendar.dateInterval(of: .weekOfYear, for: selectedDate) else {
            return []
        }

        var days: [Date] = []
        var currentDate = weekInterval.start

        for _ in 0..<7 {
            days.append(currentDate)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }

        return days
    }

    var body: some View {
        VStack(spacing: 0) {
            // Week header
            weekHeader

            // Week grid
            weekGrid
        }
        .padding(.horizontal, 16)
    }

    private var weekHeader: some View {
        HStack(spacing: 0) {
            // Time column placeholder
            Text("")
                .frame(width: 50)

            // Day headers
            ForEach(weekDays, id: \.self) { day in
                Button(action: { onDateTap(day) }) {
                    VStack(spacing: 4) {
                        Text(dayName(for: day))
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Text(dayNumber(for: day))
                            .font(.beVietnamPro(.bold, size: 16))
                            .foregroundColor(isToday(day) ? .white : AppConstants.Colors.textPrimary)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(isToday(day) ? AppConstants.Colors.primary : Color.clear)
                            )
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.bottom, 16)
    }

    private var weekGrid: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(Array(0...23), id: \.self) { hour in
                    HStack(spacing: 0) {
                        // Hour label
                        Text(String(format: "%02d:00", hour))
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .frame(width: 50, alignment: .trailing)

                        // Day columns
                        ForEach(weekDays, id: \.self) { day in
                            let dayEvents = eventsForDay(day, hour: hour)

                            VStack(spacing: 2) {
                                ForEach(dayEvents) { event in
                                    WeekEventView(event: event) {
                                        onEventTap(event)
                                    }
                                }
                            }
                            .frame(maxWidth: .infinity, minHeight: 50)
                            .background(
                                Rectangle()
                                    .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
                            )
                        }
                    }
                }
            }
        }
    }

    private func dayName(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEE"
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter.string(from: date).uppercased()
    }

    private func dayNumber(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter.string(from: date)
    }

    private func isToday(_ date: Date) -> Bool {
        Calendar.current.isDateInToday(date)
    }

    private func eventsForDay(_ day: Date, hour: Int) -> [CalendarEvent] {
        return events.filter { event in
            let calendar = Calendar.current
            return calendar.isDate(event.startDate, inSameDayAs: day) &&
                   calendar.component(.hour, from: event.startDate) == hour
        }
    }
}

// MARK: - Week Event View

struct WeekEventView: View {
    let event: CalendarEvent
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 2) {
                Text(event.title)
                    .font(.beVietnamPro(.semiBold, size: 10))
                    .foregroundColor(.white)
                    .lineLimit(1)

                Text(timeString)
                    .font(.beVietnamPro(.medium, size: 8))
                    .foregroundColor(.white.opacity(0.8))
                    .lineLimit(1)
            }
            .padding(.horizontal, 4)
            .padding(.vertical, 2)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(hex: event.courseColor))
            )
        }
    }

    private var timeString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: event.startDate)
    }
}

// MARK: - Month Calendar View

struct MonthCalendarView: View {
    let events: [CalendarEvent]
    let selectedDate: Date
    let onEventTap: (CalendarEvent) -> Void
    let onDateTap: (Date) -> Void

    private var monthDays: [Date] {
        let calendar = Calendar.current
        guard let monthInterval = calendar.dateInterval(of: .month, for: selectedDate) else {
            return []
        }

        var days: [Date] = []
        let startOfMonth = monthInterval.start

        // Get the first day of the week for the month
        let weekday = calendar.component(.weekday, from: startOfMonth)
        let daysFromPreviousMonth = (weekday - 2 + 7) % 7 // Monday = 0

        // Add days from previous month
        for i in (1...daysFromPreviousMonth).reversed() {
            if let day = calendar.date(byAdding: .day, value: -i, to: startOfMonth) {
                days.append(day)
            }
        }

        // Add days of current month
        let daysInMonth = calendar.range(of: .day, in: .month, for: selectedDate)?.count ?? 30
        for i in 0..<daysInMonth {
            if let day = calendar.date(byAdding: .day, value: i, to: startOfMonth) {
                days.append(day)
            }
        }

        // Add days from next month to complete the grid
        let totalCells = 42 // 6 weeks * 7 days
        let remainingCells = totalCells - days.count
        let lastDay = days.last ?? startOfMonth

        for i in 1...remainingCells {
            if let day = calendar.date(byAdding: .day, value: i, to: lastDay) {
                days.append(day)
            }
        }

        return days
    }

    var body: some View {
        VStack(spacing: 0) {
            // Month header
            monthHeader

            // Month grid
            monthGrid
        }
        .padding(.horizontal, 16)
    }

    private var monthHeader: some View {
        HStack {
            ForEach(["T2", "T3", "T4", "T5", "T6", "T7", "CN"], id: \.self) { day in
                Text(day)
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
            }
        }
        .padding(.bottom, 8)
    }

    private var monthGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 1) {
            ForEach(monthDays, id: \.self) { day in
                MonthDayView(
                    day: day,
                    events: eventsForDay(day),
                    isCurrentMonth: isCurrentMonth(day),
                    isToday: isToday(day),
                    isSelected: isSelected(day),
                    onDateTap: { onDateTap(day) },
                    onEventTap: onEventTap
                )
            }
        }
    }

    private func eventsForDay(_ day: Date) -> [CalendarEvent] {
        return events.filter { event in
            Calendar.current.isDate(event.startDate, inSameDayAs: day)
        }
    }

    private func isCurrentMonth(_ date: Date) -> Bool {
        Calendar.current.isDate(date, equalTo: selectedDate, toGranularity: .month)
    }

    private func isToday(_ date: Date) -> Bool {
        Calendar.current.isDateInToday(date)
    }

    private func isSelected(_ date: Date) -> Bool {
        Calendar.current.isDate(date, inSameDayAs: selectedDate)
    }
}

// MARK: - Month Day View

struct MonthDayView: View {
    let day: Date
    let events: [CalendarEvent]
    let isCurrentMonth: Bool
    let isToday: Bool
    let isSelected: Bool
    let onDateTap: () -> Void
    let onEventTap: (CalendarEvent) -> Void

    private var dayNumber: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter.string(from: day)
    }

    var body: some View {
        Button(action: onDateTap) {
            VStack(spacing: 4) {
                // Day number
                Text(dayNumber)
                    .font(.beVietnamPro(.semiBold, size: 14))
                    .foregroundColor(dayNumberColor)
                    .frame(width: 24, height: 24)
                    .background(
                        Circle()
                            .fill(dayBackgroundColor)
                    )

                // Event indicators
                if !events.isEmpty {
                    VStack(spacing: 1) {
                        ForEach(events.prefix(3)) { event in
                            Button(action: { onEventTap(event) }) {
                                RoundedRectangle(cornerRadius: 2)
                                    .fill(Color(hex: event.courseColor))
                                    .frame(height: 3)
                            }
                        }

                        if events.count > 3 {
                            Text("+\(events.count - 3)")
                                .font(.beVietnamPro(.medium, size: 8))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }
                }

                Spacer()
            }
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? AppConstants.Colors.primary.opacity(0.1) : Color.clear)
                    .stroke(isSelected ? AppConstants.Colors.primary : Color.clear, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var dayNumberColor: Color {
        if isToday {
            return .white
        } else if isCurrentMonth {
            return AppConstants.Colors.textPrimary
        } else {
            return AppConstants.Colors.textSecondary.opacity(0.5)
        }
    }

    private var dayBackgroundColor: Color {
        if isToday {
            return AppConstants.Colors.primary
        } else {
            return Color.clear
        }
    }
}



// MARK: - Calendar View

struct CalendarView: View {
    @Binding var events: [CalendarEvent]
    @Binding var selectedDate: Date
    @Binding var viewType: ScheduleViewType
    @Binding var isLoading: Bool

    let onEventTap: (CalendarEvent) -> Void
    let onDateChange: (Date) -> Void
    let onViewTypeChange: (ScheduleViewType) -> Void
    let onFilterChange: (CalendarFilter) -> Void

    @State private var currentDate = Date()
    @State private var showingDatePicker = false
    @State private var showingFilter = false
    @State private var filter = CalendarFilter.empty
    
    var body: some View {
        VStack(spacing: 0) {
            // Calendar Header
            calendarHeader
            
            // View Type Selector
            viewTypeSelector
            
            // Calendar Content
            calendarContent
        }
        .background(Color.white)
        .onAppear {
            currentDate = selectedDate
        }
    }
    
    // MARK: - Calendar Header
    
    private var calendarHeader: some View {
        HStack {
            // Previous button
            Button(action: navigatePrevious) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(AppConstants.Colors.primary.opacity(0.1))
                    )
            }
            
            Spacer()
            
            // Date title
            Button(action: { showingDatePicker = true }) {
                VStack(spacing: 2) {
                    Text(headerTitle)
                        .font(.beVietnamPro(.bold, size: 18))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(headerSubtitle)
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            // Filter button
            Button(action: { showingFilter = true }) {
                ZStack {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(filter.isActive ? .white : AppConstants.Colors.primary)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(filter.isActive ? AppConstants.Colors.primary : AppConstants.Colors.primary.opacity(0.1))
                        )

                    // Active indicator
                    if filter.isActive {
                        Circle()
                            .fill(.red)
                            .frame(width: 8, height: 8)
                            .offset(x: 12, y: -12)
                    }
                }
            }

            // Next button
            Button(action: navigateNext) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(AppConstants.Colors.primary.opacity(0.1))
                    )
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .sheet(isPresented: $showingDatePicker) {
            DatePicker(
                "Chọn ngày",
                selection: $currentDate,
                displayedComponents: [.date]
            )
            .datePickerStyle(.graphical)
            .presentationDetents([.medium])
            .presentationDragIndicator(.visible)
            .onChange(of: currentDate) { newDate in
                selectedDate = newDate
                onDateChange(newDate)
            }
        }
        .sheet(isPresented: $showingFilter) {
            CalendarFilterView(
                filter: $filter,
                isPresented: $showingFilter,
                courses: [], // TODO: Pass actual courses
                classes: []  // TODO: Pass actual classes
            )
            .onChange(of: filter) { newFilter in
                onFilterChange(newFilter)
            }
        }
    }
    
    // MARK: - View Type Selector
    
    private var viewTypeSelector: some View {
        HStack(spacing: 0) {
            ForEach(ScheduleViewType.allCases, id: \.self) { type in
                Button(action: {
                    viewType = type
                    onViewTypeChange(type)
                }) {
                    Text(type.displayName)
                        .font(.beVietnamPro(.semiBold, size: 14))
                        .foregroundColor(viewType == type ? .white : AppConstants.Colors.textSecondary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(viewType == type ? AppConstants.Colors.primary : Color.clear)
                        )
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
                .padding(.horizontal, 20)
        )
    }
    
    // MARK: - Calendar Content
    
    private var calendarContent: some View {
        Group {
            if isLoading {
                loadingView
            } else {
                switch viewType {
                case .day:
                    DayCalendarView(
                        events: eventsForCurrentPeriod,
                        selectedDate: selectedDate,
                        onEventTap: onEventTap
                    )
                case .week:
                    WeekCalendarView(
                        events: eventsForCurrentPeriod,
                        selectedDate: selectedDate,
                        onEventTap: onEventTap,
                        onDateTap: { date in
                            selectedDate = date
                            onDateChange(date)
                        }
                    )
                case .month:
                    MonthCalendarView(
                        events: eventsForCurrentPeriod,
                        selectedDate: selectedDate,
                        onEventTap: onEventTap,
                        onDateTap: { date in
                            selectedDate = date
                            onDateChange(date)
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(AppConstants.Colors.primary)
            
            Text("Đang tải lịch học...")
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
    
    // MARK: - Computed Properties
    
    private var headerTitle: String {
        let formatter = DateFormatter()
        
        switch viewType {
        case .day:
            formatter.dateFormat = "EEEE, dd/MM"
            return formatter.string(from: selectedDate)
        case .week:
            let weekStart = Calendar.current.dateInterval(of: .weekOfYear, for: selectedDate)?.start ?? selectedDate
            let weekEnd = Calendar.current.date(byAdding: .day, value: 6, to: weekStart) ?? selectedDate
            
            let startFormatter = DateFormatter()
            startFormatter.dateFormat = "dd/MM"
            let endFormatter = DateFormatter()
            endFormatter.dateFormat = "dd/MM/yyyy"
            
            return "\(startFormatter.string(from: weekStart)) - \(endFormatter.string(from: weekEnd))"
        case .month:
            formatter.dateFormat = "MMMM yyyy"
            formatter.locale = Locale(identifier: "vi_VN")
            return formatter.string(from: selectedDate).capitalized
        }
    }
    
    private var headerSubtitle: String {
        let eventsCount = eventsForCurrentPeriod.count
        return "\(eventsCount) buổi học"
    }
    
    private var eventsForCurrentPeriod: [CalendarEvent] {
        let calendar = Calendar.current
        
        switch viewType {
        case .day:
            return events.filter { calendar.isDate($0.startDate, inSameDayAs: selectedDate) }
        case .week:
            guard let weekInterval = calendar.dateInterval(of: .weekOfYear, for: selectedDate) else {
                return []
            }
            return events.filter { event in
                weekInterval.contains(event.startDate)
            }
        case .month:
            guard let monthInterval = calendar.dateInterval(of: .month, for: selectedDate) else {
                return []
            }
            return events.filter { event in
                monthInterval.contains(event.startDate)
            }
        }
    }
    
    // MARK: - Navigation Methods
    
    private func navigatePrevious() {
        let calendar = Calendar.current
        
        switch viewType {
        case .day:
            currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
        case .week:
            currentDate = calendar.date(byAdding: .weekOfYear, value: -1, to: currentDate) ?? currentDate
        case .month:
            currentDate = calendar.date(byAdding: .month, value: -1, to: currentDate) ?? currentDate
        }
        
        selectedDate = currentDate
        onDateChange(currentDate)
    }
    
    private func navigateNext() {
        let calendar = Calendar.current
        
        switch viewType {
        case .day:
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        case .week:
            currentDate = calendar.date(byAdding: .weekOfYear, value: 1, to: currentDate) ?? currentDate
        case .month:
            currentDate = calendar.date(byAdding: .month, value: 1, to: currentDate) ?? currentDate
        }
        
        selectedDate = currentDate
        onDateChange(currentDate)
    }
}

// MARK: - Preview

#Preview {
    CalendarView(
        events: .constant([]),
        selectedDate: .constant(Date()),
        viewType: .constant(.week),
        isLoading: .constant(false),
        onEventTap: { _ in },
        onDateChange: { _ in },
        onViewTypeChange: { _ in },
        onFilterChange: { _ in }
    )
}
