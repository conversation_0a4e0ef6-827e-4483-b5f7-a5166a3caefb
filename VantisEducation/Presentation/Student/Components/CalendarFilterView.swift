//
//  CalendarFilterView.swift
//  VantisEducation
//
//  Created by Mobile App Template on 31/7/25.
//

import SwiftUI

// MARK: - Calendar Filter Model

struct CalendarFilter {
    var courseId: Int?
    var classId: Int?
    var searchText: String = ""
    var showCompleted: Bool = true
    var showUpcoming: Bool = true
    var showToday: Bool = true
    var showLive: Bool = true
    
    var isActive: Bool {
        courseId != nil || classId != nil || !searchText.isEmpty || 
        !showCompleted || !showUpcoming || !showToday || !showLive
    }
    
    static let empty = CalendarFilter()
}

// MARK: - Calendar Filter View

struct CalendarFilterView: View {
    @Binding var filter: CalendarFilter
    @Binding var isPresented: Bool
    
    let courses: [StudentCourse]
    let classes: [StudentClass]
    
    @State private var tempFilter: CalendarFilter
    
    init(
        filter: Binding<CalendarFilter>,
        isPresented: Binding<Bool>,
        courses: [StudentCourse] = [],
        classes: [StudentClass] = []
    ) {
        self._filter = filter
        self._isPresented = isPresented
        self.courses = courses
        self.classes = classes
        self._tempFilter = State(initialValue: filter.wrappedValue)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search section
                searchSection
                
                // Filter content
                ScrollView {
                    VStack(spacing: 24) {
                        // Course filter
                        if !courses.isEmpty {
                            courseFilterSection
                        }
                        
                        // Class filter
                        if !classes.isEmpty {
                            classFilterSection
                        }
                        
                        // Status filter
                        statusFilterSection
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                
                // Action buttons
                actionButtons
            }
            .navigationTitle("Bộ lọc")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Hủy") {
                    isPresented = false
                },
                trailing: Button("Xóa") {
                    tempFilter = CalendarFilter.empty
                }
                .foregroundColor(.red)
            )
        }
        .onAppear {
            tempFilter = filter
        }
    }
    
    // MARK: - Search Section
    
    private var searchSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Tìm kiếm buổi học...", text: $tempFilter.searchText)
                    .font(.beVietnamPro(.medium, size: 16))
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !tempFilter.searchText.isEmpty {
                    Button(action: {
                        tempFilter.searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
        }
        .padding(.horizontal, 20)
        .padding(.top, 16)
    }
    
    // MARK: - Course Filter Section
    
    private var courseFilterSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Khóa học")
                .font(.beVietnamPro(.bold, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                // All courses option
                FilterChip(
                    title: "Tất cả",
                    isSelected: tempFilter.courseId == nil,
                    onTap: {
                        tempFilter.courseId = nil
                        tempFilter.classId = nil // Reset class when course changes
                    }
                )
                
                // Individual courses
                ForEach(courses, id: \.id) { course in
                    FilterChip(
                        title: course.name,
                        isSelected: tempFilter.courseId == course.id,
                        onTap: {
                            tempFilter.courseId = course.id
                            tempFilter.classId = nil // Reset class when course changes
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - Class Filter Section
    
    private var classFilterSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Lớp học")
                .font(.beVietnamPro(.bold, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            // Filter classes based on selected course
            let filteredClasses = tempFilter.courseId == nil ? classes : 
                classes.filter { $0.courseId == tempFilter.courseId }
            
            if filteredClasses.isEmpty {
                Text("Không có lớp học nào")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    // All classes option
                    FilterChip(
                        title: "Tất cả",
                        isSelected: tempFilter.classId == nil,
                        onTap: {
                            tempFilter.classId = nil
                        }
                    )
                    
                    // Individual classes
                    ForEach(filteredClasses, id: \.id) { classItem in
                        FilterChip(
                            title: classItem.name,
                            isSelected: tempFilter.classId == classItem.id,
                            onTap: {
                                tempFilter.classId = classItem.id
                            }
                        )
                    }
                }
            }
        }
    }
    
    // MARK: - Status Filter Section
    
    private var statusFilterSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Trạng thái buổi học")
                .font(.beVietnamPro(.bold, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                StatusToggle(
                    title: "Đang diễn ra",
                    icon: "video.fill",
                    color: .red,
                    isOn: $tempFilter.showLive
                )
                
                StatusToggle(
                    title: "Hôm nay",
                    icon: "calendar.badge.clock",
                    color: AppConstants.Colors.primary,
                    isOn: $tempFilter.showToday
                )
                
                StatusToggle(
                    title: "Sắp tới",
                    icon: "calendar",
                    color: .orange,
                    isOn: $tempFilter.showUpcoming
                )
                
                StatusToggle(
                    title: "Đã hoàn thành",
                    icon: "checkmark.circle.fill",
                    color: .green,
                    isOn: $tempFilter.showCompleted
                )
            }
        }
    }
    
    // MARK: - Action Buttons
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            Button(action: {
                filter = tempFilter
                isPresented = false
            }) {
                Text("Áp dụng")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppConstants.Colors.primary)
                    )
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color.white)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: -2)
    }
}

// MARK: - Filter Chip

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(title)
                .font(.beVietnamPro(.semiBold, size: 14))
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isSelected ? AppConstants.Colors.primary : Color.gray.opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Status Toggle

struct StatusToggle: View {
    let title: String
    let icon: String
    let color: Color
    @Binding var isOn: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 24)
            
            Text(title)
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: AppConstants.Colors.primary))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.05))
        )
    }
}

// MARK: - Supporting Models

struct StudentCourse {
    let id: Int
    let name: String
}

struct StudentClass {
    let id: Int
    let name: String
    let courseId: Int
}
