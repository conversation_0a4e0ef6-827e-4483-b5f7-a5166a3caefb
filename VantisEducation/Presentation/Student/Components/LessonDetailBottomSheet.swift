//
//  LessonDetailBottomSheet.swift
//  VantisEducation
//
//  Created by Mobile App Template on 31/7/25.
//

import SwiftUI

// MARK: - Lesson Detail Bottom Sheet

struct LessonDetailBottomSheet: View {
    let lesson: StudentLesson
    let onCheckin: () -> Void
    let onDismiss: () -> Void
    
    @State private var showingCheckinConfirmation = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Handle bar
            handleBar
            
            // Content
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Quick info
                    quickInfoSection
                    
                    // Details
                    detailsSection
                    
                    // Actions
                    actionsSection
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }
        }
        .background(Color.white)
        .cornerRadius(20, corners: [.topLeft, .topRight])
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
        .alert("Xác nhận điểm danh", isPresented: $showingCheckinConfirmation) {
            But<PERSON>("Hủy", role: .cancel) { }
            <PERSON><PERSON>("Điểm danh") {
                onCheckin()
            }
        } message: {
            Text("Bạn có chắc chắn muốn điểm danh cho buổi học này?")
        }
    }
    
    // MARK: - Handle Bar
    
    private var handleBar: some View {
        VStack(spacing: 16) {
            // Drag indicator
            RoundedRectangle(cornerRadius: 2)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 40, height: 4)
                .padding(.top, 12)
            
            // Close button
            HStack {
                Spacer()
                
                Button(action: onDismiss) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(Color.gray.opacity(0.1))
                        )
                }
            }
            .padding(.horizontal, 20)
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Status badge
            HStack {
                statusBadge
                Spacer()
            }
            
            // Title
            Text(lesson.name)
                .font(.beVietnamPro(.bold, size: 20))
                .foregroundColor(AppConstants.Colors.textPrimary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Subtitle
            Text("\(lesson.className) • \(lesson.instructorName)")
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
    
    // MARK: - Quick Info Section
    
    private var quickInfoSection: some View {
        VStack(spacing: 16) {
            // Time
            quickInfoRow(
                icon: "clock",
                title: "Thời gian",
                value: timeString,
                color: AppConstants.Colors.primary
            )
            
            // Location
            if !lesson.location.isEmpty {
                quickInfoRow(
                    icon: "location",
                    title: "Địa điểm",
                    value: locationString,
                    color: .orange
                )
            }
            
            // Duration
            quickInfoRow(
                icon: "timer",
                title: "Thời lượng",
                value: durationString,
                color: .green
            )
            
            // Subject
            if let subject = lesson.subjectName, !subject.isEmpty {
                quickInfoRow(
                    icon: "book",
                    title: "Môn học",
                    value: subject,
                    color: .purple
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
    
    // MARK: - Details Section
    
    private var detailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Chi tiết")
                .font(.beVietnamPro(.bold, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                detailRow(title: "Trạng thái", value: lessonStatusText)
                detailRow(title: "Điểm danh", value: attendanceStatusText)
                

            }
        }
    }
    
    // MARK: - Actions Section
    
    private var actionsSection: some View {
        VStack(spacing: 12) {
            // Primary action
            if lesson.canAccess && (lesson.isLive || lesson.isToday) {
                Button(action: {
                    showingCheckinConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "location.circle.fill")
                            .font(.system(size: 20))
                        
                        Text("Điểm danh")
                            .font(.beVietnamPro(.semiBold, size: 16))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppConstants.Colors.primary)
                    )
                }
            }
            
            // Secondary actions
            HStack(spacing: 12) {
                if lesson.canAccess && (lesson.hasAssignments ?? false) {
                    Button(action: {
                        // Handle quiz access
                        print("Access quiz for lesson: \(lesson.name)")
                    }) {
                        HStack {
                            Image(systemName: "questionmark.circle")
                                .font(.system(size: 16))
                            
                            Text("Bài kiểm tra")
                                .font(.beVietnamPro(.semiBold, size: 14))
                        }
                        .foregroundColor(AppConstants.Colors.primary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppConstants.Colors.primary, lineWidth: 1)
                        )
                    }
                }
                
                Button(action: {
                    // Handle materials access
                    print("Access materials for lesson: \(lesson.name)")
                }) {
                    HStack {
                        Image(systemName: "doc.text")
                            .font(.system(size: 16))
                        
                        Text("Tài liệu")
                            .font(.beVietnamPro(.semiBold, size: 14))
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(AppConstants.Colors.primary, lineWidth: 1)
                    )
                }
            }
        }
    }
    
    // MARK: - Helper Views
    
    private var statusBadge: some View {
        Text(statusText)
            .font(.beVietnamPro(.semiBold, size: 12))
            .foregroundColor(statusTextColor)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(statusBackgroundColor)
            )
    }
    
    private func quickInfoRow(icon: String, title: String, value: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.beVietnamPro(.medium, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(value)
                    .font(.beVietnamPro(.semiBold, size: 14))
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
            
            Spacer()
        }
    }
    
    private func detailRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Spacer()
            
            Text(value)
                .font(.beVietnamPro(.semiBold, size: 14))
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
    }
    
    // MARK: - Computed Properties
    
    private var statusText: String {
        if lesson.isLive {
            return "Đang diễn ra"
        } else if lesson.isToday {
            return "Hôm nay"
        } else if lesson.isUpcoming {
            return "Sắp tới"
        } else {
            return "Đã hoàn thành"
        }
    }
    
    private var statusTextColor: Color {
        if lesson.isLive {
            return .red
        } else if lesson.isToday {
            return AppConstants.Colors.primary
        } else if lesson.isUpcoming {
            return .orange
        } else {
            return .green
        }
    }
    
    private var statusBackgroundColor: Color {
        statusTextColor.opacity(0.15)
    }
    
    private var timeString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        
        if let startDate = parseDateTime(lesson.startDatetime),
           let endDate = parseDateTime(lesson.endDatetime) {
            let startTime = formatter.string(from: startDate)
            let endTime = formatter.string(from: endDate)
            
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "EEEE, dd/MM/yyyy"
            dateFormatter.locale = Locale(identifier: "vi_VN")
            let dateString = dateFormatter.string(from: startDate)
            
            return "\(dateString)\n\(startTime) - \(endTime)"
        }
        
        return "Chưa xác định"
    }
    
    private var locationString: String {
        var components: [String] = []
        
        if !lesson.room.isEmpty {
            components.append(lesson.room)
        }
        
        if !lesson.location.isEmpty {
            components.append(lesson.location)
        }
        
        return components.joined(separator: ", ")
    }
    
    private var durationString: String {
        if lesson.durationHours > 0 {
            let hours = Int(lesson.durationHours)
            let minutes = Int((lesson.durationHours - Double(hours)) * 60)
            
            if hours > 0 && minutes > 0 {
                return "\(hours) giờ \(minutes) phút"
            } else if hours > 0 {
                return "\(hours) giờ"
            } else {
                return "\(minutes) phút"
            }
        }
        
        return "Chưa xác định"
    }
    
    private var lessonStatusText: String {
        switch lesson.lessonStatus {
        case .scheduled:
            return "Đã lên lịch"
        case .inProgress:
            return "Đang diễn ra"
        case .completed:
            return "Đã hoàn thành"
        case .cancelled:
            return "Đã hủy"
        }
    }
    
    private var attendanceStatusText: String {
        switch lesson.attendanceStatus {
        case .present:
            return "Có mặt"
        case .absent:
            return "Vắng mặt"
        case .pending:
            return "Chưa điểm danh"
        }
    }
    
    // MARK: - Helper Methods
    
    private func parseDateTime(_ dateTimeString: String) -> Date? {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter.date(from: dateTimeString)
    }
}


