//
//  SimpleLessonCard.swift
//  VantisEducation
//
//  Created by Mobile App Template on 31/7/25.
//

import SwiftUI

// MARK: - Simple Lesson Card

struct SimpleLessonCard: View {
    let lesson: StudentLesson
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // Status indicator and icon
                VStack(spacing: 8) {
                    // Status dot
                    Circle()
                        .fill(statusColor)
                        .frame(width: 12, height: 12)
                    
                    // Lesson type icon
                    ZStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(iconBackground)
                            .frame(width: 36, height: 36)
                        
                        Image(systemName: iconName)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(iconColor)
                    }
                }
                
                // Lesson content
                VStack(alignment: .leading, spacing: 6) {
                    // Title
                    Text(lesson.name)
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    // Class and instructor
                    Text("\(lesson.className) • \(lesson.instructorName)")
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(1)
                    
                    // Time and location
                    HStack(spacing: 12) {
                        // Time
                        HStack(spacing: 4) {
                            Image(systemName: "clock")
                                .font(.system(size: 12))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            
                            Text(timeString)
                                .font(.beVietnamPro(.medium, size: 12))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        
                        // Location
                        if let location = lesson.location, !location.isEmpty {
                            HStack(spacing: 4) {
                                Image(systemName: "location")
                                    .font(.system(size: 12))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                
                                Text(location)
                                    .font(.beVietnamPro(.medium, size: 12))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                    .lineLimit(1)
                            }
                        }
                    }
                    
                    // Status badge
                    statusBadge
                }
                
                Spacer()
                
                // Action indicator
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white)
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Computed Properties
    
    private var statusColor: Color {
        switch lesson.lessonType {
        case .live:
            return .red
        case .today:
            return AppConstants.Colors.primary
        case .upcoming:
            return .orange
        case .completed:
            return .green
        }
    }
    
    private var iconName: String {
        switch lesson.lessonType {
        case .live:
            return "video.fill"
        case .today:
            return "calendar.badge.clock"
        case .upcoming:
            return "calendar"
        case .completed:
            return "checkmark.circle.fill"
        }
    }
    
    private var iconColor: Color {
        switch lesson.lessonType {
        case .live:
            return .red
        case .today:
            return AppConstants.Colors.primary
        case .upcoming:
            return .orange
        case .completed:
            return .green
        }
    }
    
    private var iconBackground: Color {
        iconColor.opacity(0.15)
    }
    
    private var borderColor: Color {
        switch lesson.lessonType {
        case .live:
            return .red.opacity(0.3)
        case .today:
            return AppConstants.Colors.primary.opacity(0.3)
        default:
            return Color.gray.opacity(0.2)
        }
    }
    
    private var timeString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        
        if let startDate = parseDateTime(lesson.startDatetime),
           let endDate = parseDateTime(lesson.endDatetime) {
            let startTime = formatter.string(from: startDate)
            let endTime = formatter.string(from: endDate)
            
            // Add date if not today
            let dateFormatter = DateFormatter()
            if Calendar.current.isDateInToday(startDate) {
                return "\(startTime) - \(endTime)"
            } else {
                dateFormatter.dateFormat = "dd/MM"
                let dateString = dateFormatter.string(from: startDate)
                return "\(dateString), \(startTime) - \(endTime)"
            }
        }
        
        return "Chưa xác định"
    }
    
    private var statusBadge: some View {
        HStack(spacing: 4) {
            Text(statusText)
                .font(.beVietnamPro(.semiBold, size: 10))
                .foregroundColor(statusTextColor)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(statusBackgroundColor)
                )
        }
    }
    
    private var statusText: String {
        switch lesson.lessonType {
        case .live:
            return "ĐANG DIỄN RA"
        case .today:
            return "HÔM NAY"
        case .upcoming:
            return "SẮP TỚI"
        case .completed:
            return "ĐÃ HOÀN THÀNH"
        }
    }
    
    private var statusTextColor: Color {
        switch lesson.lessonType {
        case .live:
            return .red
        case .today:
            return AppConstants.Colors.primary
        case .upcoming:
            return .orange
        case .completed:
            return .green
        }
    }
    
    private var statusBackgroundColor: Color {
        statusTextColor.opacity(0.15)
    }
    
    // MARK: - Helper Methods
    
    private func parseDateTime(_ dateTimeString: String) -> Date? {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter.date(from: dateTimeString)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 16) {
        SimpleLessonCard(
            lesson: StudentLesson(
                id: 1,
                name: "SwiftUI Fundamentals - Buổi 2",
                className: "iOS Development Bootcamp",
                subjectName: "Lập trình iOS",
                instructorName: "Trần Thị B",
                startDatetime: "2025-07-31T14:30:00+07:00",
                endDatetime: "2025-07-31T16:00:00+07:00",
                durationHours: 1.5,
                room: "Phòng 302",
                location: "Tầng 3",
                lessonStatus: "scheduled",
                lessonType: .today,
                attendanceStatus: .pending,
                canCheckin: true,
                canAccessQuiz: false,
                isCompleted: false,
                completionPercentage: 0
            )
        ) {
            print("Lesson tapped")
        }
        
        SimpleLessonCard(
            lesson: StudentLesson(
                id: 2,
                name: "Advanced SwiftUI Patterns",
                className: "iOS Development Bootcamp",
                subjectName: "Lập trình iOS",
                instructorName: "Nguyễn Văn A",
                startDatetime: "2025-07-31T16:30:00+07:00",
                endDatetime: "2025-07-31T18:30:00+07:00",
                durationHours: 2.0,
                room: "Phòng 301",
                location: "Tầng 3",
                lessonStatus: "in_progress",
                lessonType: .live,
                attendanceStatus: .pending,
                canCheckin: true,
                canAccessQuiz: true,
                isCompleted: false,
                completionPercentage: 0
            )
        ) {
            print("Lesson tapped")
        }
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
