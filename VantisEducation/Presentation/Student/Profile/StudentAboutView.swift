//
//  StudentAboutView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import SwiftUI

struct StudentAboutView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showTermsOfService = false
    @State private var showPrivacyPolicy = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Custom Header
                headerSection
                
                // App Info Section
                appInfoSection
                
                // Company Info Section
                companyInfoSection
                
                // Legal Section
                legalSection
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 20)
        }
        .navigationBarHidden(true)
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .sheet(isPresented: $showTermsOfService) {
            InAppBrowserView(
                url: URL(string: "https://vantis.edu.vn/terms")!,
                title: "<PERSON><PERSON><PERSON>u khoản sử dụng"
            )
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            InAppBrowserView(
                url: URL(string: "https://vantis.edu.vn/privacy")!,
                title: "Chính sách bảo mật"
            )
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Về ứng dụng")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Thông tin chi tiết về ứng dụng")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Empty space for balance
            Color.clear
                .frame(width: 36, height: 36)
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - App Info Section
    private var appInfoSection: some View {
        VStack(spacing: 20) {
            // App Icon and Name
            VStack(spacing: 16) {
                // App Icon
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "graduationcap.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                    )
                
                // App Name and Version
                VStack(spacing: 8) {
                    Text("Vantis Education")
                        .font(.beVietnamPro(.bold, size: 24))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text("Phiên bản \(AppConstants.AppInfo.version) (\(AppConstants.AppInfo.build))")
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            .padding(.vertical, 20)
            .frame(maxWidth: .infinity)
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
            
            // App Description
            VStack(spacing: 16) {
                HStack {
                    Text("Về ứng dụng")
                        .font(.beVietnamPro(.semiBold, size: 18))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Spacer()
                }
                
                Text("Vantis Education là ứng dụng học tập toàn diện dành cho học sinh, sinh viên. Ứng dụng cung cấp các tính năng quản lý khóa học, lịch học, bài thi và theo dõi tiến độ học tập một cách hiệu quả.")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.leading)
            }
            .padding(20)
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
    
    // MARK: - Company Info Section
    private var companyInfoSection: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Text("Thông tin công ty")
                    .font(.beVietnamPro(.semiBold, size: 18))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()
            }

            // Section Content
            VStack(spacing: 0) {
                // About Us
                Button(action: {
                    // TODO: Navigate to about us
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "building.2")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Về chúng tôi")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Tìm hiểu về sứ mệnh và giá trị của chúng tôi")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                Divider()
                    .padding(.leading, 56)

                // Contact Info
                Button(action: {
                    // TODO: Navigate to contact
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "envelope")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.success)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Liên hệ")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("<EMAIL>")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                Divider()
                    .padding(.leading, 56)

                // Website
                Button(action: {
                    openWebsite()
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "globe")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.info)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Website")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("www.student-app.com")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // External link icon
                        Image(systemName: "arrow.up.right.square")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
    
    // MARK: - Legal Section
    private var legalSection: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Text("Pháp lý")
                    .font(.beVietnamPro(.semiBold, size: 18))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()
            }

            // Section Content
            VStack(spacing: 0) {
                // Terms of Service
                Button(action: {
                    showTermsOfService = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "doc.text")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Điều khoản sử dụng")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Đọc điều khoản và điều kiện sử dụng")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                Divider()
                    .padding(.leading, 56)

                // Privacy Policy
                Button(action: {
                    showPrivacyPolicy = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "hand.raised")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.warning)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Chính sách bảo mật")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Tìm hiểu cách chúng tôi bảo vệ dữ liệu của bạn")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
    
    // MARK: - Helper Methods
    private func openWebsite() {
        if let websiteURL = URL(string: "https://www.student-app.com") {
            UIApplication.shared.open(websiteURL)
        }
    }
}

#Preview {
    StudentAboutView()
}
