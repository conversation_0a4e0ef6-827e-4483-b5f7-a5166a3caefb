//
//  StudentSupportView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import SwiftUI
import MessageUI

struct StudentSupportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showContactForm = false
    @State private var showFAQ = false
    @State private var showMailError = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Custom Header
                headerSection
                
                // Quick Help Section
                quickHelpSection
                
                // Contact Options Section
                contactOptionsSection
                
                // Resources Section
                resourcesSection
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 20)
        }
        .navigationBarHidden(true)
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .sheet(isPresented: $showContactForm) {
            StudentContactFormView()
        }
        .sheet(isPresented: $showFAQ) {
            StudentFAQView()
        }
        .alert("Mail không khả dụng", isPresented: $showMailError) {
            Button("OK") { }
        } message: {
            Text("Mail chưa được cấu hình trên thiết bị này. Vui lòng liên hệ chúng tôi tại \(AppConstants.Contact.supportEmail)")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Trợ giúp & Hỗ trợ")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Chúng tôi luôn sẵn sàng hỗ trợ bạn")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Empty space for balance
            Color.clear
                .frame(width: 36, height: 36)
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - Quick Help Section
    private var quickHelpSection: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Text("Trợ giúp nhanh")
                    .font(.beVietnamPro(.semiBold, size: 18))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()
            }

            // Quick Help Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                QuickHelpCard(
                    icon: "questionmark.circle.fill",
                    title: "FAQ",
                    subtitle: "Câu hỏi thường gặp",
                    color: AppConstants.Colors.primary
                ) {
                    showFAQ = true
                }

                QuickHelpCard(
                    icon: "book.fill",
                    title: "Hướng dẫn",
                    subtitle: "Cách sử dụng ứng dụng",
                    color: AppConstants.Colors.success
                ) {
                    // TODO: Navigate to user guide
                }

                QuickHelpCard(
                    icon: "video.fill",
                    title: "Video hướng dẫn",
                    subtitle: "Xem video hướng dẫn",
                    color: AppConstants.Colors.info
                ) {
                    // TODO: Navigate to video tutorials
                }

                QuickHelpCard(
                    icon: "message.fill",
                    title: "Chat hỗ trợ",
                    subtitle: "Trò chuyện trực tiếp",
                    color: AppConstants.Colors.accent
                ) {
                    // TODO: Open chat support
                }
            }
        }
    }
    
    // MARK: - Contact Options Section
    private var contactOptionsSection: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Text("Liên hệ với chúng tôi")
                    .font(.beVietnamPro(.semiBold, size: 18))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()
            }

            // Contact Options
            VStack(spacing: 0) {
                ContactOptionRow(
                    icon: "envelope.fill",
                    title: "Gửi email",
                    subtitle: AppConstants.Contact.supportEmail,
                    iconColor: AppConstants.Colors.primary
                ) {
                    sendEmail()
                }

                Divider()
                    .padding(.leading, 56)

                ContactOptionRow(
                    icon: "phone.fill",
                    title: "Gọi điện",
                    subtitle: AppConstants.Contact.supportPhone,
                    iconColor: AppConstants.Colors.success
                ) {
                    makePhoneCall()
                }

                Divider()
                    .padding(.leading, 56)

                ContactOptionRow(
                    icon: "paperplane.fill",
                    title: "Gửi phản hồi",
                    subtitle: "Chia sẻ ý kiến của bạn",
                    iconColor: AppConstants.Colors.info
                ) {
                    showContactForm = true
                }
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
    
    // MARK: - Resources Section
    private var resourcesSection: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Text("Tài nguyên")
                    .font(.beVietnamPro(.semiBold, size: 18))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()
            }

            // Resources
            VStack(spacing: 0) {
                ContactOptionRow(
                    icon: "globe",
                    title: "Website",
                    subtitle: AppConstants.URLs.website.replacingOccurrences(of: "https://", with: ""),
                    iconColor: AppConstants.Colors.primary
                ) {
                    openWebsite()
                }

                Divider()
                    .padding(.leading, 56)

                ContactOptionRow(
                    icon: "doc.text.fill",
                    title: "Điều khoản sử dụng",
                    subtitle: "Đọc điều khoản và điều kiện",
                    iconColor: AppConstants.Colors.secondary
                ) {
                    // TODO: Navigate to terms
                }

                Divider()
                    .padding(.leading, 56)

                ContactOptionRow(
                    icon: "hand.raised.fill",
                    title: "Chính sách bảo mật",
                    subtitle: "Tìm hiểu về quyền riêng tư",
                    iconColor: AppConstants.Colors.warning
                ) {
                    // TODO: Navigate to privacy policy
                }
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
    
    // MARK: - Helper Methods
    private func sendEmail() {
        if MFMailComposeViewController.canSendMail() {
            // TODO: Present mail composer
        } else {
            showMailError = true
        }
    }
    
    private func makePhoneCall() {
        if let phoneURL = AppConstants.Contact.phoneURL {
            UIApplication.shared.open(phoneURL)
        }
    }
    
    private func openWebsite() {
        if let websiteURL = URL(string: AppConstants.URLs.website) {
            UIApplication.shared.open(websiteURL)
        }
    }
}

// MARK: - Quick Help Card
struct QuickHelpCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 32))
                    .foregroundColor(color)
                
                // Content
                VStack(spacing: 4) {
                    Text(title)
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 20)
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Contact Option Row
struct ContactOptionRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let iconColor: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(iconColor)
                    .frame(width: 24, height: 24)
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Chevron
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Views
struct StudentContactFormView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Biểu mẫu liên hệ sẽ được triển khai sớm")
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .navigationTitle("Liên hệ")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Đóng") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

struct StudentFAQView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Nội dung FAQ sẽ được triển khai sớm")
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .navigationTitle("FAQ")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Đóng") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

#Preview {
    StudentSupportView()
}
