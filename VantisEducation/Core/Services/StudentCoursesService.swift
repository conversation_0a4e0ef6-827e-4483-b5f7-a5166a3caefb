//
//  StudentCoursesService.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 29/7/25.
//

import Foundation
import Combine

// MARK: - Student Courses Service
class StudentCoursesService: ObservableObject {
    static let shared = StudentCoursesService()

    private let apiClient: APIClient
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init(apiClient: APIClient = APIClient.shared) {
        self.apiClient = apiClient
    }
    
    // MARK: - Get Enrolled Courses
    func getEnrolledCourses(
        status: String? = nil,
        page: Int = 1,
        pageSize: Int = 20,
        sortBy: String? = nil,
        sortOrder: String = "asc"
    ) -> AnyPublisher<StudentCourseListResponse, Error> {

        // Build endpoint with query parameters
        var endpoint = "/students/courses/"
        var queryItems: [String] = []

        queryItems.append("page=\(page)")
        queryItems.append("page_size=\(pageSize)")
        queryItems.append("sort_order=\(sortOrder)")

        if let status = status {
            queryItems.append("status=\(status)")
        }

        if let sortBy = sortBy {
            queryItems.append("sort_by=\(sortBy)")
        }

        if !queryItems.isEmpty {
            endpoint += "?" + queryItems.joined(separator: "&")
        }

        // Create a Future that wraps the async API call
        return Future<StudentCourseListResponse, Error> { [weak self] promise in
            Task {
                do {
                    guard let self = self else {
                        promise(.failure(StudentCoursesAPIError.invalidURL))
                        return
                    }

                    // Add Accept-Language header for Vietnamese
                    let headers = ["Accept-Language": "vi"]

                    let response: StudentCourseListResponse = try await self.apiClient.request(
                        endpoint: endpoint,
                        method: .GET,
                        headers: headers,
                        responseType: StudentCourseListResponse.self,
                        requiresAuth: true
                    )

                    promise(.success(response))
                } catch {
                    // Convert APIClient errors to our custom errors
                    let convertedError = self?.convertError(error) ?? error
                    promise(.failure(convertedError))
                }
            }
        }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
    
    // MARK: - Get Course Detail
    func getCourseDetail(courseId: Int) -> AnyPublisher<StudentCourse, Error> {
        let endpoint = "/students/courses/course/\(courseId)"

        return Future<StudentCourse, Error> { [weak self] promise in
            Task {
                do {
                    guard let self = self else {
                        promise(.failure(StudentCoursesAPIError.invalidURL))
                        return
                    }

                    // Add Accept-Language header for Vietnamese
                    let headers = ["Accept-Language": "vi"]

                    // Backend returns StudentCourseDetailResponse, so we need to extract the data field
                    print("🌐 StudentCoursesService: Making API request to \(endpoint)")
                    let response: StudentCourseDetailResponse = try await self.apiClient.request(
                        endpoint: endpoint,
                        method: .GET,
                        headers: headers,
                        responseType: StudentCourseDetailResponse.self,
                        requiresAuth: true
                    )

                    print("🌐 StudentCoursesService: Received response - success: \(response.success)")
                    print("🌐 StudentCoursesService: Response message: \(response.message)")
                    print("🌐 StudentCoursesService: Has data: \(response.data != nil)")

                    // Extract the course data from the response
                    if response.success, let courseData = response.data {
                        print("✅ StudentCoursesService: Successfully extracted course data")
                        promise(.success(courseData))
                    } else {
                        print("❌ StudentCoursesService: Failed to extract course data - success: \(response.success), data: \(response.data != nil)")
                        promise(.failure(StudentCoursesAPIError.noData))
                    }
                } catch {
                    let convertedError = self?.convertError(error) ?? error
                    promise(.failure(convertedError))
                }
            }
        }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }

    // MARK: - Error Conversion
    private func convertError(_ error: Error) -> Error {
        // Convert NetworkError to our custom StudentCoursesAPIError
        if let networkError = error as? NetworkError {
            switch networkError {
            case .invalidURL:
                return StudentCoursesAPIError.invalidURL
            case .unauthorized:
                return StudentCoursesAPIError.unauthorized
            case .decodingError:
                return StudentCoursesAPIError.decodingError
            case .serverError(let statusCode, let message):
                return StudentCoursesAPIError.networkError(message ?? "Server error (\(statusCode))")
            case .unknown(let underlyingError):
                return StudentCoursesAPIError.networkError(underlyingError.localizedDescription)
            default:
                return StudentCoursesAPIError.networkError(error.localizedDescription)
            }
        }

        return StudentCoursesAPIError.networkError(error.localizedDescription)
    }
    

}

// MARK: - Student Courses API Error
enum StudentCoursesAPIError: Error, LocalizedError {
    case invalidURL
    case unauthorized
    case noData
    case decodingError
    case networkError(String)

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "URL không hợp lệ"
        case .unauthorized:
            return "Không có quyền truy cập"
        case .noData:
            return "Không có dữ liệu"
        case .decodingError:
            return "Lỗi xử lý dữ liệu"
        case .networkError(let message):
            return "Lỗi mạng: \(message)"
        }
    }
}

// MARK: - Course Filter Options
enum CourseFilterStatus: String, CaseIterable {
    case all = ""
    case enrolled = "enrolled"
    case pending = "pending"
    case completed = "completed"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .all: return "Tất cả"
        case .enrolled: return "Đang học"
        case .pending: return "Chờ xử lý"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        }
    }
}

enum CourseSortOption: String, CaseIterable {
    case enrollmentDate = "enrollment_date"
    case courseName = "name"
    case progress = "progress_percentage"
    case status = "enrollment_status"
    
    var displayName: String {
        switch self {
        case .enrollmentDate: return "Ngày đăng ký"
        case .courseName: return "Tên khóa học"
        case .progress: return "Tiến độ"
        case .status: return "Trạng thái"
        }
    }
}

enum CoursesSortOrder: String, CaseIterable {
    case asc = "asc"
    case desc = "desc"

    var displayName: String {
        switch self {
        case .asc: return "Tăng dần"
        case .desc: return "Giảm dần"
        }
    }
}
