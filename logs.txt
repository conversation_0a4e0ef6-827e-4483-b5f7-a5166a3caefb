ℹ️ [INFO] 2025-07-31 11:30:26.532 | MobileApp.swift:161 application(_:didFinishLaunchingWithOptions:) | App launched
App launched
🎓 StudentAuth: Restored session for student
🧪 Testing DateTime Parsing...
📅 Testing: '2025-06-17T18:00:00'
🔍 LessonAdapter: Parsing datetime: '2025-06-17T18:00:00'
🔍 LessonAdapter: Parsed with custom formatter (UTC): 2025-06-17 18:00:00 +0000
🔍 LessonAdapter: Parsing datetime: '2025-06-17T18:00:00'
🔍 LessonAdapter: Parsed with custom formatter (UTC): 2025-06-17 18:00:00 +0000
   LessonAdapter - isToday: false, isUpcoming: false
   StudentLesson extension methods - testing datetime parsing...
📅 Testing: '2025-07-31T11:14:00'
🔍 LessonAdapter: Parsing datetime: '2025-07-31T11:14:00'
🔍 LessonAdapter: Parsed with custom formatter (UTC): 2025-07-31 11:14:00 +0000
🔍 LessonAdapter: Parsing datetime: '2025-07-31T11:14:00'
🔍 LessonAdapter: Parsed with custom formatter (UTC): 2025-07-31 11:14:00 +0000
   LessonAdapter - isToday: true, isUpcoming: true
   StudentLesson extension methods - testing datetime parsing...
📅 Testing: '2025-08-01T09:30:00'
🔍 LessonAdapter: Parsing datetime: '2025-08-01T09:30:00'
🔍 LessonAdapter: Parsed with custom formatter (UTC): 2025-08-01 09:30:00 +0000
🔍 LessonAdapter: Parsing datetime: '2025-08-01T09:30:00'
🔍 LessonAdapter: Parsed with custom formatter (UTC): 2025-08-01 09:30:00 +0000
   LessonAdapter - isToday: false, isUpcoming: true
   StudentLesson extension methods - testing datetime parsing...
🧪 DateTime Parsing Tests Completed!
🔐 MobileApp: Checking authentication state on startup
🎓 StudentAuth: Restored session for student
✅ Successfully registered font: BeVietnamPro-Regular
✅ Successfully registered font: BeVietnamPro-Medium
✅ Successfully registered font: BeVietnamPro-SemiBold
✅ Successfully registered font: BeVietnamPro-Bold
⚠️ TokenManager: DEPRECATED - Use SecureTokenManager for secure token storage
🔍 [DEBUG] 2025-07-31 11:30:26.664 | AnalyticsService.swift:52 track(event:properties:) | Analytics event tracked: session_start
Analytics event tracked: session_start
🔍 [DEBUG] 2025-07-31 11:30:26.664 | AnalyticsService.swift:52 track(event:properties:) | Analytics event tracked: app_launch
Analytics event tracked: app_launch
🔐 MobileApp: App setup complete - auth state: false
🔐 ContentView: Received isAuthenticated change = false
🔐 ContentView: Received currentUser change = nil
🔐 ContentView: Showing LoadingView - checking auth state
🔐 ContentView: Received isAuthenticated change = true
🔐 ContentView: Received currentUser change = Lê Văn Hùng
🔐 ContentView: Received isAuthenticated change = true
🔐 ContentView: Received currentUser change = Lê Văn Hùng
ℹ️ [INFO] 2025-07-31 11:30:26.711 | SyncService.swift:64 stopPeriodicSync() | Stopped periodic sync
Stopped periodic sync
ℹ️ [INFO] 2025-07-31 11:30:26.711 | SyncService.swift:58 startPeriodicSync() | Started periodic sync with interval: 60.0s
Started periodic sync with interval: 60.0s
ℹ️ [INFO] 2025-07-31 11:30:26.711 | SyncService.swift:64 stopPeriodicSync() | Stopped periodic sync
Stopped periodic sync
ℹ️ [INFO] 2025-07-31 11:30:26.711 | SyncService.swift:58 startPeriodicSync() | Started periodic sync with interval: 60.0s
Started periodic sync with interval: 60.0s
🔍 [DEBUG] 2025-07-31 11:30:26.711 | AnalyticsService.swift:52 track(event:properties:) | Analytics event tracked: app_foreground
Analytics event tracked: app_foreground
ℹ️ [INFO] 2025-07-31 11:30:26.712 | ConfigurationService.swift:41 loadConfiguration() | Using default configuration (backend endpoints not implemented yet)
Using default configuration (backend endpoints not implemented yet)
ℹ️ [INFO] 2025-07-31 11:30:26.712 | ConfigurationService.swift:41 loadConfiguration() | Using default configuration (backend endpoints not implemented yet)
Using default configuration (backend endpoints not implemented yet)
ℹ️ [INFO] 2025-07-31 11:30:26.712 | SyncService.swift:84 performSync(force:) | Starting data synchronization
Starting data synchronization
⚠️ TokenManager.getToken: DEPRECATED - Use SecureTokenManager.getToken()
ℹ️ [INFO] 2025-07-31 11:30:26.712 | SyncService.swift:89 performSync(force:) | Skipping sync - user not authenticated
Skipping sync - user not authenticated
🔐 ContentView: Auth state initialized, hiding loading
🎯 StudentTabView: onAppear called - ModernTabView should be visible now
🔐 RoleBasedTabView: Showing StudentTabView for STUDENT user - ID: , Name: Lê Văn Hùng
🔐 RoleBasedTabView: onAppear - currentUser: Optional(VantisEducation.User(id: "", email: "", firstName: Optional("Lê"), lastName: Optional("Văn Hùng"), phone: nil, role: VantisEducation.UserRole.student, isActive: true, avatar: nil, dateOfBirth: nil, lastLoginAt: Optional(2025-07-31 04:30:26 +0000), createdAt: 2025-07-31 04:30:26 +0000, updatedAt: Optional(2025-07-31 04:30:26 +0000), businessName: nil, businessId: nil, category: nil, businessPhone: nil, website: nil, businessDescription: nil, businessStatus: nil, onboardedAt: nil))
🔐 RoleBasedTabView: onAppear - User role: student
🔐 ContentView: Showing RoleBasedTabView - user is authenticated
🔐 ContentView: Auth state initialized, hiding loading
🔐 ContentView: Auth state initialized, hiding loading
🔥 Tab clicked: Lịch học (ID: 2)
🎯 Selecting tab: Lịch học (ID: 2)
🔐 SecureTokenManager: Migration already completed
📋 StudentScheduleViewModel: Setup observers - simplified version
🔍 [DEBUG] 2025-07-31 11:30:29.732 | AnalyticsService.swift:52 track(event:properties:) | Analytics event tracked: screen_view
Analytics event tracked: screen_view
🎯 StudentScheduleView: Task started - loading initial data...
🎯 StudentScheduleView: Task completed
🔄 StudentScheduleViewModel: Starting to load lessons...
🔍 StudentScheduleViewModel: Calling API endpoint: /students/lessons/
🔍 StudentScheduleViewModel: Parameters - page: 1, pageSize: 50, classId: nil
🔥 API Call - Endpoint: /students/lessons/
🔥 API Call - Parameters: ["page_size": 50, "page": 1]
🔐 SecureTokenManager: Access token retrieved from Keychain
🔐 SecureTokenManager: Token preview: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3N...
🔐 APIClient: Using Bearer token for authentication (length: 705)
🔐 APIClient: Token preview: eyJhbGciOiJIUzI1NiIs...
🔗 APIClient: Updated URL with query parameters: https://lms-dev.ebill.vn/api/v1/students/lessons/?page_size=50&page=1
🌐 API Request to: https://lms-dev.ebill.vn/api/v1/students/lessons/
🌐 Method: GET
🌐 Headers: ["Content-Type": "application/json", "User-Agent": "MobileApp/1.0", "Accept": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bqnvi2HppFu40fwdpkqLt4P7d_WjCk58sSU1ZBPgKPg"]
🌐 Response Status: 200
🌐 Response Body: {"success":true,"message":"Tìm thấy 7 buổi học","data":[{"id":8,"name":"TÀI CHÍNH CHO CEO - 17/06/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"ThS. Nguyễn Chiến Trường","start_datetime":"2025-06-18T01:00:00+07:00","end_datetime":"2025-06-18T03:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":9,"name":"TÀI CHÍNH CHO CEO - 20/06/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-06-21T01:00:00+07:00","end_datetime":"2025-06-21T03:00:00+07:00","duration_hours":2.0,"room":"102","location":"791 Phan Đăng Lưu","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":22,"name":"TÀI CHÍNH CHO CEO - 15/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-15T18:00:00+07:00","end_datetime":"2025-07-15T20:00:00+07:00","duration_hours":2.0,"room":"102","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":21,"name":"Test api","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"CG. Ngô Minh Hải","start_datetime":"2025-07-23T06:00:00+07:00","end_datetime":"2025-07-23T12:00:00+07:00","duration_hours":6.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":23,"name":"TÀI CHÍNH CHO CEO - 21/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-23T18:00:00+07:00","end_datetime":"2025-07-23T20:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":25,"name":"TÀI CHÍNH CHO CEO - 28/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-28T18:00:00+07:00","end_datetime":"2025-07-28T20:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":32,"name":"TÀI CHÍNH CHO CEO - 29/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-29T18:00:00+07:00","end_datetime":"2025-07-29T20:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"}],"pagination":{"page":1,"page_size":50,"total_count":7,"total_pages":1,"has_next":false,"has_previous":false}}
🔥 API Response - Success: true
🔥 API Response - Message: Tìm thấy 7 buổi học
🔥 API Response - Data count: 7
🔥 API Response - First lesson: TÀI CHÍNH CHO CEO - 17/06/2025
📦 StudentScheduleViewModel: Received response - success: true, data count: 7
📦 StudentScheduleViewModel: Response message: Tìm thấy 7 buổi học
✅ StudentScheduleViewModel: Lessons loaded - count: 7
📚 Lesson 1: TÀI CHÍNH CHO CEO - 17/06/2025 - isToday: false, isUpcoming: false, isLive: false
📚 Lesson 2: TÀI CHÍNH CHO CEO - 20/06/2025 - isToday: false, isUpcoming: false, isLive: false
📚 Lesson 3: TÀI CHÍNH CHO CEO - 15/07/2025 - isToday: false, isUpcoming: false, isLive: false
🏁 StudentScheduleViewModel: Load lessons completed - isLoading: false, lessons count: 7
🔥 Schedule API Call - Endpoint: /students/my-schedule
🔥 Schedule API Call - Parameters: ["page": 1, "date_to": "2025-08-04", "date_from": "2025-07-28", "page_size": 100, "view": "week"]
🔐 SecureTokenManager: Access token retrieved from Keychain
🔐 SecureTokenManager: Token preview: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3N...
🔐 APIClient: Using Bearer token for authentication (length: 705)
🔐 APIClient: Token preview: eyJhbGciOiJIUzI1NiIs...
🔗 APIClient: Updated URL with query parameters: https://lms-dev.ebill.vn/api/v1/students/my-schedule?page=1&date_to=2025-08-04&date_from=2025-07-28&page_size=100&view=week
🌐 API Request to: https://lms-dev.ebill.vn/api/v1/students/my-schedule
🌐 Method: GET
🌐 Headers: ["Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bqnvi2HppFu40fwdpkqLt4P7d_WjCk58sSU1ZBPgKPg", "User-Agent": "MobileApp/1.0", "Accept": "application/json"]
🌐 Response Status: 200
🌐 Response Body: {"success":true,"message":"Tìm thấy 2 buổi học","data":[{"id":25,"name":"TÀI CHÍNH CHO CEO - 28/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-28T18:00:00+07:00","end_datetime":"2025-07-28T20:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":32,"name":"TÀI CHÍNH CHO CEO - 29/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-29T18:00:00+07:00","end_datetime":"2025-07-29T20:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"}],"pagination":{"page":1,"page_size":100,"total_count":2,"total_pages":1,"has_next":false,"has_previous":false}}
🔥 Schedule API Response - Success: true
🔥 Schedule API Response - Message: Tìm thấy 2 buổi học
🔥 Schedule API Response - Data count: 2
✅ StudentScheduleViewModel: Calendar events loaded - count: 2
🔥 Schedule API Call - Endpoint: /students/my-schedule
🔥 Schedule API Call - Parameters: ["date_from": "2025-07-31", "page": 1, "date_to": "2025-07-31", "page_size": 100, "view": "day"]
🔐 SecureTokenManager: Access token retrieved from Keychain
🔐 SecureTokenManager: Token preview: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3N...
🔐 APIClient: Using Bearer token for authentication (length: 705)
🔐 APIClient: Token preview: eyJhbGciOiJIUzI1NiIs...
🔗 APIClient: Updated URL with query parameters: https://lms-dev.ebill.vn/api/v1/students/my-schedule?date_from=2025-07-31&page=1&date_to=2025-07-31&page_size=100&view=day
🌐 API Request to: https://lms-dev.ebill.vn/api/v1/students/my-schedule
🌐 Method: GET
🌐 Headers: ["Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bqnvi2HppFu40fwdpkqLt4P7d_WjCk58sSU1ZBPgKPg", "User-Agent": "MobileApp/1.0"]
🌐 Response Status: 200
🌐 Response Body: {"success":true,"message":"Tìm thấy 0 buổi học","data":[],"pagination":{"page":1,"page_size":100,"total_count":0,"total_pages":0,"has_next":false,"has_previous":false}}
🔥 Schedule API Response - Success: true
🔥 Schedule API Response - Message: Tìm thấy 0 buổi học
🔥 Schedule API Response - Data count: 0
✅ StudentScheduleViewModel: Calendar events loaded - count: 0
🔄 StudentScheduleViewModel: Starting to load lessons...
🔍 StudentScheduleViewModel: Calling API endpoint: /students/lessons/
🔍 StudentScheduleViewModel: Parameters - page: 1, pageSize: 50, classId: nil
🔥 API Call - Endpoint: /students/lessons/
🔥 API Call - Parameters: ["page": 1, "page_size": 50]
🔐 SecureTokenManager: Access token retrieved from Keychain
🔐 SecureTokenManager: Token preview: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3N...
🔐 APIClient: Using Bearer token for authentication (length: 705)
🔐 APIClient: Token preview: eyJhbGciOiJIUzI1NiIs...
🔗 APIClient: Updated URL with query parameters: https://lms-dev.ebill.vn/api/v1/students/lessons/?page=1&page_size=50
🌐 API Request to: https://lms-dev.ebill.vn/api/v1/students/lessons/
🌐 Method: GET
🌐 Headers: ["Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bqnvi2HppFu40fwdpkqLt4P7d_WjCk58sSU1ZBPgKPg", "Accept": "application/json", "User-Agent": "MobileApp/1.0"]
🌐 Response Status: 200
🌐 Response Body: {"success":true,"message":"Tìm thấy 7 buổi học","data":[{"id":8,"name":"TÀI CHÍNH CHO CEO - 17/06/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"ThS. Nguyễn Chiến Trường","start_datetime":"2025-06-18T01:00:00+07:00","end_datetime":"2025-06-18T03:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":9,"name":"TÀI CHÍNH CHO CEO - 20/06/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-06-21T01:00:00+07:00","end_datetime":"2025-06-21T03:00:00+07:00","duration_hours":2.0,"room":"102","location":"791 Phan Đăng Lưu","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":22,"name":"TÀI CHÍNH CHO CEO - 15/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-15T18:00:00+07:00","end_datetime":"2025-07-15T20:00:00+07:00","duration_hours":2.0,"room":"102","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":21,"name":"Test api","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"CG. Ngô Minh Hải","start_datetime":"2025-07-23T06:00:00+07:00","end_datetime":"2025-07-23T12:00:00+07:00","duration_hours":6.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":23,"name":"TÀI CHÍNH CHO CEO - 21/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-23T18:00:00+07:00","end_datetime":"2025-07-23T20:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":25,"name":"TÀI CHÍNH CHO CEO - 28/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-28T18:00:00+07:00","end_datetime":"2025-07-28T20:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"},{"id":32,"name":"TÀI CHÍNH CHO CEO - 29/07/2025","class_name":"TÀI CHÍNH CHO CEO-DB1 - Lớp mới","subject_name":"TÀI CHÍNH CHO CEO","instructor_name":"Lê Như Hiếu","start_datetime":"2025-07-29T18:00:00+07:00","end_datetime":"2025-07-29T20:00:00+07:00","duration_hours":2.0,"room":"101","location":"513 Điện Biên Phủ","attendance_status":"absent","lesson_status":"scheduled","can_access":true,"is_today":false,"is_upcoming":false,"is_live":false,"needs_attention":true,"attention_reason":"Vắng mặt"}],"pagination":{"page":1,"page_size":50,"total_count":7,"total_pages":1,"has_next":false,"has_previous":false}}
🔥 API Response - Success: true
🔥 API Response - Message: Tìm thấy 7 buổi học
🔥 API Response - Data count: 7
🔥 API Response - First lesson: TÀI CHÍNH CHO CEO - 17/06/2025
📦 StudentScheduleViewModel: Received response - success: true, data count: 7
📦 StudentScheduleViewModel: Response message: Tìm thấy 7 buổi học
✅ StudentScheduleViewModel: Lessons loaded - count: 7
📚 Lesson 1: TÀI CHÍNH CHO CEO - 17/06/2025 - isToday: false, isUpcoming: false, isLive: false
📚 Lesson 2: TÀI CHÍNH CHO CEO - 20/06/2025 - isToday: false, isUpcoming: false, isLive: false
📚 Lesson 3: TÀI CHÍNH CHO CEO - 15/07/2025 - isToday: false, isUpcoming: false, isLive: false
🏁 StudentScheduleViewModel: Load lessons completed - isLoading: false, lessons count: 7