//
//  AppConstants.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation
import SwiftUI

struct AppConstants {

    // MARK: - App Information
    static let appName = "Mobile App"
    static let appTagline = "Your Digital Experience Hub"
    static let appVersion = "1.0.0"

    // MARK: - API Configuration
    struct API {
        static let baseURL = "https://lms-dev.ebill.vn/api/v1/"
        static let timeout: TimeInterval = 30.0
        static let retryAttempts = 3
        static let retryDelay: TimeInterval = 1.0

        // Endpoints
        struct Endpoints {
            static let auth = "/auth"
            static let users = "/users"
            static let transactions = "/transactions"
            static let items = "/items"
            static let categories = "/categories"
            static let orders = "/orders"
            static let instructors = "/instructors"
        }
    }
    
    // MARK: - UI Constants
    struct UI {
        static let cornerRadius: CGFloat = 12
        static let shadowRadius: CGFloat = 4
        static let buttonHeight: CGFloat = 50
        static let screenPadding: CGFloat = 16
        static let sectionSpacing: CGFloat = 24
        static let itemSpacing: CGFloat = 16
        static let cardPadding: CGFloat = 16
        
        // Animation
        static let animationDuration: Double = 0.3
        static let springAnimation = Animation.spring(response: 0.5, dampingFraction: 0.8)
        static let easeInOutAnimation = Animation.easeInOut(duration: animationDuration)
    }
    
    // MARK: - Colors (Light Mode Only)
    struct Colors {
        // Primary Colors
        static let primary = Color(red: 0.2, green: 0.6, blue: 1.0) // Blue
        static let primaryDeep = Color(red: 0.1, green: 0.4, blue: 0.8) // Darker shade of primary
        static let primaryLight = Color(red: 0.6, green: 0.8, blue: 1.0)

        // Secondary Colors
        static let secondary = Color(red: 1.0, green: 0.6, blue: 0.2) // Orange
        static let accent = Color(red: 0.8, green: 0.2, blue: 0.8) // Purple

        // Status Colors
        static let success = Color(red: 0.2, green: 0.8, blue: 0.4) // Green
        static let warning = Color(red: 1.0, green: 0.8, blue: 0.0) // Yellow
        static let error = Color(red: 1.0, green: 0.3, blue: 0.3) // Red
        static let info = Color(red: 0.3, green: 0.7, blue: 1.0) // Light Blue

        // Neutral Colors - Fixed Light Mode Colors
        static let background = Color(red: 0.98, green: 0.98, blue: 0.98) // #FAFAFA
        static let surface = Color.white
        static let surfaceSecondary = Color(red: 0.95, green: 0.95, blue: 0.95) // #F2F2F2
        static let cardBackground = Color.white
        static let border = Color(red: 0.9, green: 0.9, blue: 0.9) // #E5E5E5
        static let shadow = Color.black

        // Text Colors - Fixed Light Mode Colors
        static let textPrimary = Color.black
        static let textSecondary = Color(red: 0.4, green: 0.4, blue: 0.4) // #666666
        static let textTertiary = Color(red: 0.6, green: 0.6, blue: 0.6) // #999999
    }
    
    // MARK: - Typography
    struct Typography {
        static let largeTitle = Font.beVietnamProLargeTitle
        static let title = Font.beVietnamProTitle
        static let title2 = Font.beVietnamProTitle2
        static let title3 = Font.beVietnamProTitle3
        static let headline = Font.beVietnamProHeadline
        static let subheadline = Font.beVietnamProSubheadline
        static let body = Font.beVietnamProBody
        static let callout = Font.beVietnamProCallout
        static let footnote = Font.beVietnamProFootnote
        static let caption = Font.beVietnamProCaption
        static let caption2 = Font.beVietnamProCaption2
    }
    
    // MARK: - Validation
    struct Validation {
        static let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        static let phoneRegex = "^[+]?[0-9]{10,15}$"
        static let minPasswordLength = 8
        static let maxPasswordLength = 128
        static let minNameLength = 2
        static let maxNameLength = 50
    }
    
    // MARK: - Storage Keys
    struct StorageKeys {
        static let accessToken = "access_token"
        static let refreshToken = "refresh_token"
        static let userId = "user_id"
        static let biometricEnabled = "biometric_enabled"
        static let notificationsEnabled = "notifications_enabled"
        static let selectedLanguage = "selected_language"
        static let onboardingCompleted = "onboarding_completed"
        static let lastSyncDate = "last_sync_date"
    }
    
    // MARK: - Keychain Keys
    struct KeychainKeys {
        static let service = "com.vantis.instructor"
        static let accessToken = "access_token"
        static let refreshToken = "refresh_token"
        static let userPrivateKey = "user_private_key"
        static let biometricData = "biometric_data"
    }

    // MARK: - Biometric
    struct Biometric {
        static let reason = "Authenticate to access your account"
        static let fallbackTitle = "Use Passcode"
        static let cancelTitle = "Cancel"
    }

    // MARK: - Transaction
    struct Transaction {
        static let exchangeRate: Double = 1.0 // 1 TOKEN = 1 USD (customize as needed)
        static let minTransferAmount: Double = 1.0
        static let maxTransferAmount: Double = 10000.0
        static let networkFee: Double = 0.001 // Network fee (customize as needed)
    }
    
    // MARK: - Pagination
    struct Pagination {
        static let defaultLimit = 20
        static let maxLimit = 100
    }
    
    // MARK: - Cache
    struct Cache {
        static let maxAge: TimeInterval = 300 // 5 minutes
        static let maxSize = 50 * 1024 * 1024 // 50 MB
    }
    
    // MARK: - Haptic Types
    enum HapticType {
        case light
        case medium
        case heavy
        case success
        case warning
        case error
    }
    
    // MARK: - App Information
    struct AppInfo {
        static let name = "YourApp"
        static let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
        static let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        static let bundleId = Bundle.main.bundleIdentifier ?? "com.vantis.instructor"

        static var fullVersion: String {
            return "\(version) (\(build))"
        }
    }
    
    // MARK: - Feature Flags
    struct FeatureFlags {
        static let biometricLoginEnabled = true
        static let socialLoginEnabled = false
        static let darkModeEnabled = false // DISABLED - Light mode only
        static let analyticsEnabled = true
        static let crashReportingEnabled = true
        static let debugMenuEnabled = false

        #if DEBUG
        static let loggingEnabled = true
        static let networkLoggingEnabled = true
        #else
        static let loggingEnabled = false
        static let networkLoggingEnabled = false
        #endif
    }
    
    // MARK: - URLs
    struct URLs {
        static let website = "https://vantis.edu.vn"
        static let support = "https://vantis.edu.vn/contact"
        static let termsOfService = "https://vantis.edu.vn/terms"
        static let privacyPolicy = "https://vantis.edu.vn/privacy"
        static let appStore = "https://apps.apple.com/app/vantis-instructor"
        static let facebook = "https://www.facebook.com/vantis.edu.vn"
    }

    // MARK: - Contact Information
    struct Contact {
        static let supportEmail = "<EMAIL>"
        static let supportPhone = "0989-030-330"
        static let trainingEmail = "<EMAIL>"
        static let feedbackEmail = "<EMAIL>"
        static let businessEmail = "<EMAIL>"
        static let address = "131 Trần Huy Liệu, phường Phú Nhuận, TPHCM"
        static let fullAddress = "131 Trần Huy Liệu, phường Phú Nhuận, Thành phố Hồ Chí Minh, Việt Nam"
    }

    // MARK: - Social Media
    struct SocialMedia {
        static let facebook = "https://facebook.com/vantis"
        static let twitter = "https://twitter.com/vantis"
        static let instagram = "https://instagram.com/vantis"
        static let linkedin = "https://linkedin.com/company/vantis"
    }

    // MARK: - Social Media
    struct SocialMedia {
        static let facebook = "https://facebook.com/yourapp"
        static let twitter = "https://twitter.com/yourapp"
        static let instagram = "https://instagram.com/yourapp"
        static let linkedin = "https://linkedin.com/company/yourapp"
    }
    
    // MARK: - Notification Types
    enum NotificationType: String, CaseIterable, Codable {
        case itemUpdated = "item_updated"
        case orderCompleted = "order_completed"
        case transactionCompleted = "transaction_completed"
        case promotionalOffer = "promotional_offer"
        case systemUpdate = "system_update"
        case securityAlert = "security_alert"

        var displayName: String {
            switch self {
            case .itemUpdated:
                return "Item Updated"
            case .orderCompleted:
                return "Order Completed"
            case .transactionCompleted:
                return "Transaction Completed"
            case .promotionalOffer:
                return "Promotional Offer"
            case .systemUpdate:
                return "System Update"
            case .securityAlert:
                return "Security Alert"
            }
        }

        var icon: String {
            switch self {
            case .itemUpdated:
                return "plus.circle.fill"
            case .orderCompleted:
                return "gift.fill"
            case .transactionCompleted:
                return "checkmark.circle.fill"
            case .promotionalOffer:
                return "megaphone.fill"
            case .systemUpdate:
                return "arrow.clockwise.circle.fill"
            case .securityAlert:
                return "shield.fill"
            }
        }
    }
    
    // MARK: - Environment
    enum Environment {
        case development
        case staging
        case production
        
        static var current: Environment {
            #if DEBUG
            return .development
            #elseif STAGING
            return .staging
            #else
            return .production
            #endif
        }
        
        var apiBaseURL: String {
            switch self {
            case .development:
                return "https://lms-dev.ebill.vn/api/v1/"
            case .staging:
                return "https://lms-dev.ebill.vn/api/v1/"
            case .production:
                return "https://lms-dev.ebill.vn/api/v1/"
            }
        }
        
        var displayName: String {
            switch self {
            case .development:
                return "Development"
            case .staging:
                return "Staging"
            case .production:
                return "Production"
            }
        }
    }
}

// MARK: - Extensions for easier access
extension AppConstants.Colors {
    static var allColors: [Color] {
        return [primary, secondary, accent, success, warning, error, info]
    }
}

extension AppConstants.UI {
    static var defaultPadding: EdgeInsets {
        return EdgeInsets(top: itemSpacing, leading: screenPadding, bottom: itemSpacing, trailing: screenPadding)
    }
}

extension AppConstants.FeatureFlags {
    static func isEnabled(_ flag: String) -> Bool {
        // This could be extended to read from remote config
        switch flag {
        case "biometric_login":
            return biometricLoginEnabled
        case "social_login":
            return socialLoginEnabled
        case "dark_mode":
            return false // Always disabled
        case "analytics":
            return analyticsEnabled
        case "crash_reporting":
            return crashReportingEnabled
        case "debug_menu":
            return debugMenuEnabled
        default:
            return false
        }
    }
}

extension AppConstants.Contact {
    static var formattedPhone: String {
        return supportPhone.replacingOccurrences(of: "-", with: "")
    }

    static var phoneURL: URL? {
        return URL(string: "tel:\(formattedPhone)")
    }

    static var emailURL: URL? {
        return URL(string: "mailto:\(supportEmail)")
    }

    static var trainingEmailURL: URL? {
        return URL(string: "mailto:\(trainingEmail)")
    }

    static var addressURL: URL? {
        let encodedAddress = fullAddress.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        return URL(string: "https://maps.google.com/?q=\(encodedAddress)")
    }
}
